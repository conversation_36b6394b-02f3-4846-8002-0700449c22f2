{% extends "admin_doc/base_docs.html" %}
{% load i18n jazzmin %}
{% get_jazzmin_ui_tweaks as jazzmin_ui %}

{% block breadcrumbs %}
<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
    <li class="breadcrumb-item"><a href="{% url 'django-admindocs-docroot' %}">{% trans 'Documentation' %}</a></li>
    <li class="breadcrumb-item"><a href="{% url 'django-admindocs-views-index' %}">{% trans 'Views' %}</a></li>
    <li class="breadcrumb-item active">{{ name }}</li>
</ol>
{% endblock %}

{% block title %}{% blocktrans %}View: {{ name }}{% endblocktrans %}{% endblock %}
{% block content_title %}{{ name }}{% endblock %}

{% block docs_content %}
    <h3 class="subhead">{{ summary|striptags }}</h3>

    {{ body }}

    {% if meta.Context %}
        <h4>{% trans 'Context:' %}</h4>
        <p>{{ meta.Context }}</p>
    {% endif %}

    {% if meta.Templates %}
        <h4>{% trans 'Templates:' %}</h4>
        <p>{{ meta.Templates }}</p>
    {% endif %}

    <div class="row">
        <div class="col-lg-3 col-sm-1"></div>
        <div class="col-lg-6 col-sm-10">
            <a class="btn btn-block {{ jazzmin_ui.button_classes.info }}" href="{% url 'django-admindocs-views-index' %}">{% trans 'Back to View documentation' %}</a>
        </div>
        <div class="col-lg-3 col-sm-1"></div>
    </div>
{% endblock %}

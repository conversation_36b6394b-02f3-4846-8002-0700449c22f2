{% extends "admin_doc/base_docs.html" %}
{% load i18n %}

{% block coltype %}colSM{% endblock %}

{% block breadcrumbs %}
<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
    <li class="breadcrumb-item"><a href="{% url 'django-admindocs-docroot' %}">{% trans 'Documentation' %}</a></li>
    <li class="breadcrumb-item active">{% trans 'Models' %}</li>
</ol>
{% endblock %}

{% block title %}{% trans 'Models' %}{% endblock %}
{% block content_title %}{% trans 'Models' %}{% endblock %}

{% block docs_content %}

{% regroup models by app_config as grouped_models %}

<div class="row">
    <div class="col-4 col-sm-2">
        <div class="nav flex-column nav-tabs h-100" id="tabs-tab" role="tablist" aria-orientation="vertical">
            {% for group in grouped_models %}
                <a class="nav-link" id="tabs-{{ group.grouper.label }}-control" data-toggle="pill" href="#tabs-{{ group.grouper.label }}" role="tab" aria-controls="tabs-{{ group.grouper.label }}" aria-selected="true">
                    <h6>{{ group.grouper.verbose_name }}</h6>
                </a>
            {% endfor %}
        </div>
    </div>

    <div class="col-7 col-sm-9">
        <div class="tab-content" id="tabs-tabContent">
            {% for group in grouped_models %}
                <div class="tab-pane text-left fade{% if forloop.first %} show active{% endif %}" id="tabs-{{ group.grouper.label }}" role="tabpanel" aria-labelledby="tabs-{{ group.grouper.label }}-control">

                    <ul>
                        {% for model in group.list %}
                            <li>
                                <a href="{% url 'django-admindocs-models-detail' app_label=model.app_label model_name=model.model_name %}">
                                    {{ model.object_name }}
                                </a>
                            </li>
                        {% endfor %}
                    </ul>

                </div>
            {% endfor %}
        </div>
    </div>
</div>

{% endblock %}

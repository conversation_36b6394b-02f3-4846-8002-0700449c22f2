{% extends "admin_doc/base_docs.html" %}
{% load i18n %}

{% block coltype %}colSM{% endblock %}

{% block breadcrumbs %}
<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
    <li class="breadcrumb-item"><a href="{% url 'django-admindocs-docroot' %}">{% trans 'Documentation' %}</a></li>
    <li class="breadcrumb-item active">{% trans 'Filters' %}</li>
</ol>
{% endblock %}

{% block title %}{% trans 'Template filters' %}{% endblock %}
{% block content_title %}{% trans 'Template filters' %}{% endblock %}

{% block docs_content %}

{% regroup filters|dictsort:"library" by library as filter_libraries %}

  <div class="row">
    <div class="col-4 col-sm-2">
      <div class="nav flex-column nav-tabs h-100" id="filter-tab" role="tablist" aria-orientation="vertical">
        {% for library in filter_libraries %}
          <a class="nav-link" id="tabs-{% firstof library.grouper built_in_filters %}-tab" data-toggle="pill" href="#tabs-{% firstof library.grouper built_in_filters %}" role="tab" aria-controls="tabs-{% firstof library.grouper built_in_filters %}" aria-selected="true"><h6>{% firstof library.grouper _("Built-in filters") %}</h6></a>
        {% endfor %}
      </div>
    </div>

    <div class="col-7 col-sm-9">
      <div class="tab-content">
        {% for library in filter_libraries %}
        <div class="tab-pane text-left fade{% if forloop.first %} show active{% endif %}" id="tabs-{% firstof library.grouper built_in_filters %}" role="tabpanel" aria-labelledby="tabs-{% firstof library.grouper built_in_filters %}-tab">
          {% for view in ns_views.list|dictsort:"url" %}
              {% ifchanged %}
              <h4><a href="{% url 'django-admindocs-views-detail' view=view.full_name %}">{{ view.url }}</a></h4>
              <p class="small quiet">{% blocktrans with view.full_name as full_name and view.url_name as url_name %}View function: <code>{{ full_name }}</code>. Name: <code>{{ url_name }}</code>.{% endblocktrans %}</p>
              <p>{{ view.title }}</p>
              <hr>
              {% endifchanged %}
          {% endfor %}

          {% if library.grouper %}<p class="small quiet">{% blocktrans with code="{"|add:"% load "|add:library.grouper|add:" %"|add:"}" %}To use these filters, put <code>{{ code }}</code> in your template before using the filter.{% endblocktrans %}</p><hr>{% endif %}

          {% for filter in library.list|dictsort:"name" %}
            <h4 id="{{ library.grouper|default:"built_in" }}-{{ filter.name }}">{{ filter.name }}</h4>
            {{ filter.title }}
            {{ filter.body }}
            {% if not forloop.last %}<hr>{% endif %}
          {% endfor %}
        </div>
        {% endfor %}
      </div>
    </div>
  </div>

{% endblock %}

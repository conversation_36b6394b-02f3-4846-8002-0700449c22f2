{% extends "base.html" %}
{% load static %}

{% block content %}

    <!-- Start Banner Area -->
    <section class="banner-area organic-breadcrumb">
        <div class="container">
            <div class="breadcrumb-banner d-flex flex-wrap align-items-center justify-content-end">
                <div class="col-first">
                    <h1>Checkout</h1>
                    <nav class="d-flex align-items-center">
                        <a href="index.html">Home<span class="lnr lnr-arrow-right"></span></a>
                        <a href="single-product.html">Checkout</a>
                    </nav>
                </div>
            </div>
        </div>
    </section>
    <!-- End Banner Area -->

    <!--================Checkout Area =================-->
    <section class="checkout_area section_gap">
        <div class="container">
            {% if not user.is_authenticated %}
            <div class="returning_customer">
                <div class="check_title">
                    <h2>Returning Customer? <a href="{% url 'login' %}">Click here to login</a></h2>
                </div>
                <p>If you have shopped with us before, please login to continue. If you are a new customer, please proceed to the Billing & Shipping section.</p>
            </div>
            {% endif %}
            <div class="cupon_area">
                <div class="check_title">
                    <h2>Have a coupon? <a href="#">Click here to enter your code</a></h2>
                </div>
                <input type="text" placeholder="Enter coupon code">
                <a class="tp_btn" href="#">Apply Coupon</a>
            </div>
            <div class="billing_details">
                <div class="row">
                    <div class="col-lg-8">
                        <h3>Billing Details</h3>
                        <form class="row contact_form" action="{% url 'checkout' %}" method="post" novalidate="novalidate">
                            {% csrf_token %}

                            <div class="col-md-6 form-group">
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger">{{ form.first_name.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 form-group">
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger">{{ form.last_name.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 form-group">
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger">{{ form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 form-group">
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="col-md-12 form-group">
                                {{ form.address_line_1 }}
                                {% if form.address_line_1.errors %}
                                    <div class="text-danger">{{ form.address_line_1.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="col-md-12 form-group">
                                {{ form.address_line_2 }}
                                {% if form.address_line_2.errors %}
                                    <div class="text-danger">{{ form.address_line_2.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 form-group">
                                {{ form.city }}
                                {% if form.city.errors %}
                                    <div class="text-danger">{{ form.city.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 form-group">
                                {{ form.state }}
                                {% if form.state.errors %}
                                    <div class="text-danger">{{ form.state.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="col-md-12 form-group">
                                {{ form.country }}
                                {% if form.country.errors %}
                                    <div class="text-danger">{{ form.country.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="col-md-12 form-group">
                                <button type="submit" class="primary-btn">Place Order</button>
                            </div>
                        </form>
                    </div>
                    <div class="col-lg-4">
                        <div class="order_box">
                            <h2>Your Order</h2>
                            <ul class="list">
                                <li><a href="#">Product <span>Total</span></a></li>
                                {% for cart_item in cart_items %}
                                <li><a href="#">{{ cart_item.product.product_name }}
                                    <span class="middle">x {{ cart_item.quantity }}</span>
                                    <span class="last">${{ cart_item.sub_total|floatformat:2 }}</span></a>
                                </li>
                                {% endfor %}
                            </ul>
                            <ul class="list list_2">
                                <li><a href="#">Subtotal <span>${{ total|floatformat:2 }}</span></a></li>
                                <li><a href="#">Tax <span>${{ tax|floatformat:2 }}</span></a></li>
                                <li><a href="#"><strong>Total <span>${{ grand_total|floatformat:2 }}</span></strong></a></li>
                            </ul>
                            <div class="payment_item">
                                <div class="radion_btn">
                                    <input type="radio" id="f-option5" name="selector">
                                    <label for="f-option5">Check payments</label>
                                    <div class="check"></div>
                                </div>
                                <p>Please send a check to Store Name, Store Street, Store Town, Store State / County,
                                    Store Postcode.</p>
                            </div>
                            <div class="payment_item active">
                                <div class="radion_btn">
                                    <input type="radio" id="f-option6" name="selector">
                                    <label for="f-option6">Paypal </label>
                                    <img src="img/product/card.jpg" alt="">
                                    <div class="check"></div>
                                </div>
                                <p>Pay via PayPal; you can pay with your credit card if you don’t have a PayPal
                                    account.</p>
                            </div>
                            <div class="creat_account">
                                <input type="checkbox" id="f-option4" name="selector">
                                <label for="f-option4">I’ve read and accept the </label>
                                <a href="#">terms & conditions*</a>
                            </div>
                            <a class="primary-btn" href="{% url 'payment' %}">Proceed to Paypal</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!--================End Checkout Area =================-->

    {% endblock content %}
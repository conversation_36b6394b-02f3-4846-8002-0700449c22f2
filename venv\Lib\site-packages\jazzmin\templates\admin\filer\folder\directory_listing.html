{% extends "admin/filer/base_site.html" %}
{% load i18n static filer_admin_tags %}

{% block extrahead %}
    {{ block.super }}

    {% if action_form and actions_on_top or actions_on_bottom %}
        <script type="text/javascript">
            (function($) {
                $(document).ready(function($) {
                    $('tr input.action-select').actions();
                });
            })(django.jQuery);
        </script>
    {% endif %}
{% endblock %}

{% block coltype %}{% endblock %}
{% block bodyclass %}change-list filebrowser{% endblock %}


{% block extrastyle %}
    {{ block.super }}

    {{ media.css }}
    {% if action_form %}
        {% url 'admin:jsi18n' as jsi18nurl %}
        <script type="text/javascript" src="{{ jsi18nurl|default:'../../jsi18n/' }}"></script>
    {% endif %}
    {% if query.pop %}
        <style type="text/css">
            #header {
                display: none;
            }
        </style>
    {% endif %}
{% endblock %}

{% block breadcrumbs %}
   {% if not is_popup %}
   <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'admin:index' %}" title="{% trans 'Go back to admin homepage' %}"><i class="fas fa-tachometer-alt"></i> {% trans 'Home' %}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'admin:app_list' app_label='filer' %}">Файлы</a></li>
        {% if not folder.is_root and folder.is_smart_folder %}
            <li class="breadcrumb-item"><a href="{% url 'admin:filer-directory_listing-root' %}" title="{% trans 'Go back to root folder' %}">{% trans "root" %}</a></li>
        {% endif %}
        {% for ancestor_folder in folder.logical_path %}
            <li class="breadcrumb-item">
                <a href="{{ ancestor_folder.get_admin_directory_listing_url_path }}"
                    title="{% blocktrans with ancestor_folder.name as folder_name %}Go back to '{{ folder_name }}' folder{% endblocktrans %}">
                    {% if ancestor_folder.label %}{{ ancestor_folder.label }}{% else %}{{ ancestor_folder.name }}{% endif %}
                </a>
            </li>
        {% endfor %}
        {% if breadcrumbs_action %}
            <li class="breadcrumb-item">
                <a href="{{ folder.get_admin_directory_listing_url_path }}"
                title="{% blocktrans with folder.name as folder_name %}Go back to '{{ folder_name }}' folder{% endblocktrans %}">
                    {% if folder.label %}
                        {{ folder.label }}
                    {% else %}
                        {{ folder.name }}
                    {% endif %}
                </a>
            </li>
            <li class="breadcrumb-item active">{{ breadcrumbs_action }}</li>
        {% else %}
            {% if folder.label %}
                <li class="breadcrumb-item active">{{ folder.label }}</li>
            {% else %}
                <li class="breadcrumb-item active">{{ folder.name }}</li>
            {% endif %}
        {% endif %}
    </ol>


        <div class="breadcrumbs">

        </div>
    {% endif %}
{% endblock %}

{% block content %}
    <div class="navigator-top-nav clearfix">
        <div class="breadcrumbs-container-wrapper">
            <div class="breadcrumbs-container">
                <div class="breadcrumbs-container-inner">
                    {% for ancestor_folder in instance.logical_path %}
                        {{ ancestor_folder }}
                    {% endfor %}

                    <div class="navigator-breadcrumbs">
                        <div class="filer-navigator-breadcrumbs-dropdown-container filer-dropdown-container">
                            <a href="#" data-toggle="filer-dropdown" aria-expanded="false">
                                <img src="{% static 'filer/icons/breadcrubms_dropdown_28_28.png' %}" alt="" width="28" height="28">
                            </a>
                            <ul class="filer-dropdown-menu navigator-breadcrumbs-dropdown">
                                {% for folder in folder.logical_path %}
                                    <li>
                                        <a href="{% url 'admin:filer-directory_listing' folder.id %}{% filer_admin_context_url_params %}"
                                            title="{% trans 'Go back to the parent folder' %}">
                                            <img src="{% static 'filer/icons/plainfolder_48x48.png' %}" alt="{% trans 'Folder Icon' %}" width="28" height="28">
                                            {{ folder.name }}
                                        </a>
                                    </li>
                                {% endfor %}
                                <li>
                                    <a href="{% url 'admin:filer-directory_listing-root' %}{% filer_admin_context_url_params %}"
                                        title="{% trans 'Go back to' %} {% trans 'root'|title %} {% trans 'folder' %}">
                                        <img src="{% static 'filer/icons/root_28x28.png' %}" alt="{% trans 'Folder Icon' %}" width="28" height="28">
                                        Root
                                    </a>
                                </li>
                            </ul>
                        </div>
                        {% if not folder.is_root or folder.is_smart_folder %}
                            <span class="icon fa fa-chevron-right"></span>
                        {% endif %}
                    </div>
                    <div class="navigator-breadcrumbs-name-dropdown-wrapper">
                        {% if not folder.is_root or folder.is_smart_folder %}

                            <div class="navigator-breadcrumbs-folder-name-wrapper">
                                <span class="navigator-breadcrumbs-folder-name">
                                    <span class="navigator-breadcrumbs-folder-name-inner">
                                        {{ folder.name }}
                                    </span>
                                </span>
                            </div>

                            <div class="filer-dropdown-container filer-dropdown-container-down">
                                {% if not is_popup and folder.file_type == 'Folder' and permissions.has_edit_permission %}
                                    <a href="#" data-toggle="filer-dropdown" aria-expanded="false">
                                        <span class="fa fa-caret-down"></span>
                                    </a>
                                    <ul class="filer-dropdown-menu">
                                        <li>
                                            <a href="{% url 'admin:filer_folder_change' folder.id %}" title="{% trans 'Change current folder details' %}">{% trans "Change" %}</a>
                                        </li>
                                    </ul>
                                {% endif %}
                            </div>
                        {% endif %}
                        <div class="empty-filer-header-cell"></div>
                    </div>
                    <form class="filter-files-container js-filter-files-container" action="." method="get" class="js-filer-search-form">
                        <div class="filter-filers-container-inner">
                            <button class="navigator-button filter-files-button" title="{% trans 'Click here to run search for entered phrase' %}"><span class="icon fa fa-search"></span></button>
                            <div class="filter-search-wrapper">
                                {% filer_admin_context_hidden_formfields %}
                                <input type="text" placeholder="{% trans 'Search' %}" class="filter-files-field js-filter-files" value="{{ search_string }}" name="q">
                                <div class="filer-dropdown-container filer-dropdown-container-down">
                                    <a href="#" data-toggle="filer-dropdown" aria-expanded="false">
                                        <span class="fa fa-caret-down"></span>
                                    </a>
                                </div>
                                <ul class="filer-dropdown-menu filer-dropdown-menu-checkboxes">
                                    <span class="fa fa-close js-close-dropdown-menu-checkboxes"><span class="sr-only">{% trans "Close" %}</span></span>
                                    <li>
                                        <p>{% trans "Limit" %}</p>
                                        <label>
                                            <input type="checkbox" id="limit_search_to_folder"
                                               name="limit_search_to_folder"
                                               {% if limit_search_to_folder %}checked="checked"{% endif %}
                                               title="{% trans 'Check it to limit the search to current folder' %}">
                                            {% trans "Limit the search to current folder" %}
                                        </label>
                                    </li>
                                </ul>
                            </div>
                        </div>
                </form>
                </div>
            </div>
            <div class="tools-container">
                {% block object-tools %}
                    <div class="navigator-tools">
                        {% if not is_popup %}
                            <div class="actions-wrapper">
                                <ul class="actions-separated-list">
                                    <li>
                                        <a href="#" class="js-action-delete" title="{% trans 'Delete' %}"><span class="fa fa-trash"></span></a>
                                    </li>
                                    <li>
                                        <a href="#" class="js-action-copy" title="{% trans 'Copy' %}"><span class="fa fa-copy"></span></a>
                                    </li>
                                    <li>
                                        <a href="#" class="js-action-move" title="{% trans 'Move' %}"><span class="fa fa-cut"></span></a>
                                    </li>
                                </ul>
                                <div class="filer-dropdown-container js-actions-menu">
                                    <a href="#" data-toggle="filer-dropdown" aria-expanded="false" class="nav-button nav-button-dots">
                                        <span class="fa fa-ellipsis-h"></span>
                                    </a>
                                    <ul class="create-menu-dropdown filer-dropdown-menu">
                                        {# This list is populated in javascript #}
                                    </ul>
                                </div>
                            </div>
                        {% endif %}
                        {% block object-tools-items %}
                            <div class="navigator-button-wrapper">
                                {% if folder.can_have_subfolders and can_make_folder %}
                                    <a href="{% url 'admin:filer-directory_listing-make_root_folder' %}?parent_id={{ folder.id }}{% if is_popup %}&amp;_popup=1{% endif %}"
                                        title="{% trans 'Adds a new Folder' %}"
                                        class="navigator-button"
                                        onclick="return showAddAnotherPopup(this);">
                                        {% trans "New Folder" %}
                                    </a>
                                {% endif %}

                                {% if permissions.has_add_children_permission and not folder.is_root %}
                                    <a href="#" id="id_upload_button" title="{% trans 'Upload Files' %}"
                                       class="navigator-button navigator-button-upload js-upload-button"
                                       data-url="{% url 'admin:filer-ajax_upload' folder_id=folder.id %}"
                                       data-max-uploader-connections="{{ uploader_connections }}">
                                        {% trans "Upload Files" %}
                                    </a>
                                {% elif folder.is_unsorted_uploads %}
                                    <a href="#" id="id_upload_button" title="{% trans 'Upload Files' %}"
                                       class="navigator-button navigator-button-upload js-upload-button"
                                       data-url="{% url 'admin:filer-ajax_upload' %}"
                                       data-max-uploader-connections="{{ uploader_connections }}">
                                        {% trans "Upload Files" %}
                                    </a>
                                {% endif %}
                                {% if folder.is_root and not folder.is_unsorted_uploads %}
                                    <span class="js-upload-button-disabled upload-button-disabled js-filer-tooltip filer-tooltip-wrapper"
                                          title="{% trans 'You have to select a folder first' %}">
                                        <a href="#" class="navigator-button navigator-button-upload" disabled>
                                            {% trans "Upload Files" %}
                                        </a>
                                    </span>
                                {% endif %}
                            </div>
                        {% endblock %}
                    </div>
                {% endblock %}
            </div>
        </div>
    </div>
    <div id="content-main">
        {% include "admin/filer/tools/search_form.html" %}
        <div class="js-navigator navigator{% if not actions_on_top and not actions_on_bottom %}navigator-no-actions{% endif %}">
            <form class="js-navigator-form" method="post">
                {% csrf_token %}
                {% filer_admin_context_hidden_formfields %}
                {% if action_form and actions_on_top and paginator.count and not is_popup %}
                    {% filer_actions %}
                {% endif %}
                {% include "admin/filer/folder/directory_table_list.html" %}
                {% if action_form and actions_on_bottom and paginator.count and not is_popup %}
                    {% filer_actions %}
                {% endif %}
            </form>
        </div>
    </div>
{% endblock %}

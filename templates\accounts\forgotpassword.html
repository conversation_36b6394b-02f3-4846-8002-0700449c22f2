{% extends "base.html" %}
{% load static %}

{% block content %}
<!-- Forgot Password Styling -->
<style>
.forgot-password-container {
    background: #f8f9fa;
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 40px 0;
}

.forgot-password-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
    max-width: 500px;
    margin: 0 auto;
}

.forgot-password-header {
    background: linear-gradient(135deg, #ffba00, #ff6c00);
    color: white;
    padding: 40px 30px;
    text-align: center;
}

.forgot-password-header h2 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.forgot-password-header p {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}

.forgot-password-body {
    padding: 40px 30px;
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-control:focus {
    outline: none;
    border-color: #ffba00;
    background: white;
    box-shadow: 0 0 0 3px rgba(255, 186, 0, 0.1);
}

.btn-reset {
    background: linear-gradient(135deg, #ffba00, #ff6c00);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    width: 100%;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-reset:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 186, 0, 0.3);
}

.btn-reset:active {
    transform: translateY(0);
}

.back-to-login {
    text-align: center;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
}

.back-to-login a {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.back-to-login a:hover {
    color: #ffba00;
    text-decoration: none;
}

.info-box {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 25px;
    color: #0c5460;
}

.info-box i {
    color: #17a2b8;
    margin-right: 8px;
}

.email-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 18px;
}

.input-wrapper {
    position: relative;
}

@media (max-width: 768px) {
    .forgot-password-container {
        padding: 20px;
    }

    .forgot-password-header {
        padding: 30px 20px;
    }

    .forgot-password-header h2 {
        font-size: 1.5rem;
    }

    .forgot-password-body {
        padding: 30px 20px;
    }

    .form-control {
        padding: 12px;
        font-size: 14px;
    }

    .btn-reset {
        padding: 12px 25px;
        font-size: 14px;
    }
}
</style>

<!-- Forgot Password Form -->
<div class="forgot-password-container">
    <div class="container">
        <div class="forgot-password-card">
            <!-- Header -->
            <div class="forgot-password-header">
                <h2><i class="ti-lock"></i> Forgot Password?</h2>
                <p>No worries! Enter your email and we'll send you reset instructions</p>
            </div>

            <!-- Form Body -->
            <div class="forgot-password-body">
                <!-- Info Box -->
                <div class="info-box">
                    <i class="ti-info-alt"></i>
                    <strong>How it works:</strong> Enter your email address and we'll send you a secure link to reset your password.
                </div>

                <!-- Form -->
                <form method="post" action="{% url 'forgotpassword' %}">
                    {% csrf_token %}

                    <div class="form-group">
                        <label for="email" class="form-label">
                            <i class="ti-email"></i> Email Address
                        </label>
                        <div class="input-wrapper">
                            <input
                                type="email"
                                class="form-control"
                                id="email"
                                name="email"
                                placeholder="Enter your registered email address"
                                required
                                autocomplete="email"
                            >
                            <i class="ti-email email-icon"></i>
                        </div>
                    </div>

                    <button type="submit" class="btn-reset">
                        <i class="ti-email"></i> Send Reset Link
                    </button>
                </form>

                <!-- Back to Login -->
                <div class="back-to-login">
                    <a href="{% url 'login' %}">
                        <i class="ti-arrow-left"></i> Back to Login
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock content %}
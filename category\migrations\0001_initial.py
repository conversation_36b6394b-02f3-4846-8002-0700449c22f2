# Generated by Django 5.2.1 on 2025-06-04 11:00

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category_name', models.<PERSON><PERSON><PERSON><PERSON>(max_length=50, unique=True)),
                ('slug', models.Char<PERSON>ield(max_length=100, unique=True)),
                ('description', models.Char<PERSON>ield(blank=True, max_length=255)),
                ('cat_image', models.ImageField(blank=True, upload_to='photos/category')),
            ],
            options={
                'verbose_name': 'category',
                'verbose_name_plural': 'categories',
            },
        ),
    ]

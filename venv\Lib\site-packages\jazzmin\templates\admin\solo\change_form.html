{% extends "admin/change_form.html" %}
{% load admin_urls i18n jazzmin %}
{% get_jazzmin_ui_tweaks as jazzmin_ui %}

{% block breadcrumbs %}
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a></li>
        <li class="breadcrumb-item active">{{ opts.verbose_name|capfirst }}</li>
    </ol>
{% endblock %}

{% block object-tools-items %}
    <a class="btn btn-block {{ jazzmin_ui.button_classes.secondary }} btn-sm" href="{% url opts|admin_urlname:'history' %}">{% trans 'History' %}</a>
    {% if has_absolute_url %}
        <a href="{% url 'admin:view_on_site' content_type_id original.pk %}" class="btn btn-block {{ jazzmin_ui.button_classes.secondary }} btn-sm">{% trans "View on site" %}</a>
    {% endif %}
{% endblock %}

{% load i18n static jazzmin admin_urls %}
{% get_current_language as LANGUAGE_CODE %}
{% get_current_language_bidi as LANGUAGE_BIDI %}
{% get_jazzmin_settings request as jazzmin_settings %}
{% get_jazzmin_ui_tweaks as jazzmin_ui %}

<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE|default:"en-us" }}" {% if LANGUAGE_BIDI %}dir="rtl"{% endif %}>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="x-ua-compatible" content="ie=edge">

    <title>{% block title %}{{ title }} | {{ jazzmin_settings.site_title }}{% endblock %}</title>

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="{% static "vendor/fontawesome-free/css/all.min.css" %}">

    <!-- <PERSON><PERSON><PERSON> and adminLTE -->
    <link rel="stylesheet" href="{% static "vendor/adminlte/css/adminlte.min.css" %}">

    <!-- Bootswatch theme -->
    {% if jazzmin_ui.theme.name != 'default' %}
        <link rel="stylesheet" href="{{ jazzmin_ui.theme.src }}" id="jazzmin-theme" />
    {% endif %}

    {% if jazzmin_ui.dark_mode_theme %}
        <link rel="stylesheet" href="{{ jazzmin_ui.dark_mode_theme.src }}" id="jazzmin-dark-mode-theme" media="(prefers-color-scheme: dark)"/>
    {% endif %}

    <!-- Custom fixes for django -->
    <link rel="stylesheet" href="{% static "jazzmin/css/main.css" %}">

    {% if jazzmin_settings.custom_css %}
        <!-- Custom CSS -->
        <link rel="stylesheet" href="{% static jazzmin_settings.custom_css %}">
    {% endif %}

    <!-- favicons -->
    <link rel="shortcut icon" href="{% static jazzmin_settings.site_icon %}" type="image/png">
    <link rel="icon" href="{% static jazzmin_settings.site_icon %}" sizes="32x32" type="image/png">

    {% if jazzmin_settings.use_google_fonts_cdn %}
    <!-- Google Font: Source Sans Pro -->
    <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700" rel="stylesheet">
    {% endif %}

    {% block extrastyle %} {% endblock %}
    {% block extrahead %} {% endblock %}
</head>
<body class="hold-transition jazzmin-login-page">

<div class="login-box">
    <div class="login-logo">
        <h1>
            <picture>
                <source
                    srcset="{% static jazzmin_settings.login_logo_dark %}"
                    media="(prefers-color-scheme: dark)">
                 <img src="{% static jazzmin_settings.login_logo %}" alt="{{ jazzmin_settings.site_header }}">
            </picture>
        </h1>
    </div>

    <div class="card">
        <div class="card-body">
            {% block content %} {% endblock %}
        </div>
    </div>
</div>

<!-- jQuery -->
<script src="{% static "admin/js/vendor/jquery/jquery.js" %}"></script>
<!-- Bootstrap 4 -->
<script src="{% static 'vendor/bootstrap/js/bootstrap.min.js' %}"></script>
<!-- AdminLTE App -->
<script src="{% static 'vendor/adminlte/js/adminlte.min.js' %}"></script>

{% if jazzmin_settings.custom_js %}
    <script src="{% static jazzmin_settings.custom_js %}"></script>
{% endif %}

</body>
</html>

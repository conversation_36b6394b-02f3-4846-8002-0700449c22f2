#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-10-03 17:04+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: templates/admin/base.html:125
msgid "Account"
msgstr "Cuenta"

#: templates/admin/base.html:143
msgid "See Profile"
msgstr "Ver perfil"

#: templates/admin/base.html:183 templates/admin/index.html:6
#: templates/admin/index.html:11
msgid "Dashboard"
msgstr "Panel de control"

#: templates/admin/base.html:294
msgid "Jazzmin version"
msgstr "Versión de Jazzmin"

#: templates/admin/base.html:297
msgid "Copyright"
msgstr ""

#: templates/admin/base.html:297
msgid "All rights reserved."
msgstr "Todos los derechos reservados."

#: templates/admin/base.html:313
msgid "UI Configuration"
msgstr "Configuración de la IU"

#: templates/admin/base.html:317
msgid "Copy this info your settings file to persist these UI changes"
msgstr ""
"Copia esta información en tu archivo settings para mantener los cambios en "
"la IU"

#: templates/admin/index.html:90
#| msgid "%(entry.action_time|timesince)s ago"
msgid "%(timesince)s ago"
msgstr "Hace %(timesince)s"

#: templates/admin/submit_line.html:8
msgid "Actions"
msgstr "Acciones"

#: templates/admin_doc/template_detail.html:13
#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Plantilla: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#: templates/admin_doc/template_detail.html:17
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Buscar ruta para plantilla <q>%(name)s</q>:"

#: templates/admin_doc/template_filter_index.html:37
#: templates/admin_doc/view_index.html:43
#, python-format
msgid ""
"View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</code>."
msgstr ""
"Ver función: <code>%(full_name)s</code>. Nombre: <code>%(url_name)s</code>."

#: templatetags/jazzmin.py:390
#, python-brace-format
msgid "Added {name} “{object}”."
msgstr "Añadido {name} “{object}”."

#: templatetags/jazzmin.py:406
#, python-brace-format
msgid "Deleted “{object}”."
msgstr "“{object}” borrado"

#: utils.py:66
#, python-brace-format
msgid "Could not reverse url from {instance}"
msgstr "No se pudo obtener la url para {instance}"

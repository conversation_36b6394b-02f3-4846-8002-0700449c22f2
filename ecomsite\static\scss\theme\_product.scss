/* Feature Product Area css
============================================================================================ */
.feature_product_area{
	.main_box{
		box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.05);
		padding-top: 30px;
		padding-bottom: 30px;
		margin-top: -50px;
		margin-bottom: -50px;
		position: relative;
		z-index: 10;
	}
	.hot_product_inner{
		margin-bottom: 100px;
	}
}
.hot_product_inner{
	
}
.hot_p_item{
	position: relative;
	.product_text{
		position: absolute;
		left: 0px;
		top: 0px;
		height: 100%;
		h4{
			margin-top: 26px;
			margin-left: 26px;
			color: $title-color;
			font-family: $primary-font;
			font-weight: 500;
			font-size: 30px;
		}
		a{
			position: absolute;
			left: 26px;
			bottom: 28px;
			text-transform: uppercase;
			font-size: 14px;
			font-weight: 500;
			color: $title-color;
			@include transition;
			&:hover{
				color: $primary-color;
			}
		}
	}
}

.feature_product_inner{}
.feature_p_slider{
	.owl-dots{
		text-align: center;
		.owl-dot{
			height: 8px;
			width: 8px;
			border-radius: 50%;
			background: #e4ecee;
			display: inline-block;
			margin-right: 10px;
			@include transition;
			margin-top: 45px;
			margin-bottom: 30px;
			&:last-child{
				margin-right: 0px;
			}
			&.active{
				width: 30px;
				height: 8px;
				background: $primary-color;
				border-radius: 4px;
			}
		}
	}
}
.f_p_item{
	text-align: center;
	margin-bottom: 35px;
	.f_p_img{
		position: relative;
		overflow: hidden;
		.p_icon{
			position: absolute;
			right: -45px;
			bottom: 30px;
			transition: all 300ms ease;
			a{
				display: block;
				height: 40px;
				width: 40px;
				text-align: center;
				border-radius: 3px;
				background: #fff;
				line-height: 40px;
				color: $title-color;
				margin-bottom: 5px;
				&:last-child{
					margin-bottom: 0px;
				}
				&:hover{
					color: #fff;
					background: $primary-color;
				}
			}
		}
	}
	h4{
		color: $title-color;
		font-size: 16px;
		margin-top: 15px;
		@include transition;
		&:hover{
			color: $primary-color;
		}
	}
	h5{
		margin-bottom: 0px;
		font-size: 18px;
		color: $text-color;
	}
	&:hover{
		.f_p_img{
			.p_icon{
				right: 15px;
			}
		}
	}
}
.latest_product_area{
	.main_box{
		padding-top: 100px;
		padding-bottom: 100px;
	}
	.feature_product_inner{
		
	}
}
.latest_product_inner{
	margin-bottom: -35px;
}
/* End Feature Product Area css
============================================================================================ */

/* End Feature Product Area css
============================================================================================ */
.most_product_area{
	.main_box{
		box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.05);
		padding-top: 100px;
		padding-bottom: 100px;
		margin-top: -50px;
		margin-bottom: -50px;
		position: relative;
		z-index: 10;
	}
}
.most_product_inner{
	margin-bottom: -30px;
}
.most_p_list{
	.media{
		margin-bottom: 30px;
		.d-flex{
			padding-right: 20px;
		}
		.media-body{
			vertical-align: middle;
			align-self: center;
			h4{
				margin-bottom: 8px;
				font-size: 14px;
				font-weight: normal;
				color: $title-color;
			}
			h3{
				font-size: 16px;
				font-weight: 500;
				color: $text-color;
				margin-bottom: 0px;
			}
		}
	}
}
/* End Feature Product Area css
============================================================================================ */

/* Product Sidebar Area css
============================================================================================ */
.cat_product_area{
	.latest_product_inner{
		padding-top: 30px;
		margin-bottom: -50px;
		.f_p_item{
			margin-bottom: 50px;
		}
	}
}
.left_sidebar_area{}
.left_widgets{
	margin-bottom: 30px;
	background-color: rgb(255, 255, 255);
	box-shadow: 0px 10px 10px 0px rgba(153, 153, 153, 0.1);
	&:last-child{
		margin-bottom: 0px;
	}
}
.widgets_inner{
	padding-left: 30px;
	padding-right: 30px;
	padding-top: 15px;
	padding-bottom: 15px;
}
.l_w_title{
	background: #e8f0f2;
	padding-left: 30px;
	h3{
		margin-bottom: 0px;
		font-size: 14px;
		color: $title-color;
		text-transform: uppercase;
		font-weight: 500;
		line-height: 60px;
	}
}
.cat_widgets{
	.list{
		li{
			margin-bottom: 13px;
			a{
				font-size: 14px;
				font-family: $primary-font;
				color: $title-color;
			}
			.list{
				margin-top: 10px;
				padding-left: 35px;
				border-top: 1px solid #eeeeee;
				padding-top: 10px;
				display: none;
			}
		}
	}
}
.p_filter_widgets{
	.widgets_inner{
		border-bottom: 1px solid #eeeeee;
		&:last-child{
			border-bottom: 0px;
		}
	}
	h4{
		color: $title-color;
		font-size: 14px;
		font-family: $primary-font;
		font-weight: normal;
		margin-bottom: 22px;
		margin-top: 10px;
	}
	.list{
		li{
			margin-bottom: 18px;
			a{
				padding-left: 30px;
				font-size: 14px;
				font-family: $primary-font;
				font-weight: normal;
				color: $text-color;
				position: relative;
				&:before{
					content: "";
					height: 10px;
					width: 10px;
					border-radius: 50%;
					border: 1px solid $text-color;
					position: absolute;
					left: 0px;
					top: 5px;
					@include transition;
				}
				&:after{
					content: "";
					height: 4px;
					width: 4px;
					background: #fff;
					border-radius: 50%;
					position: absolute;
					left: 3px;
					top: 8px;
					@include transition;
				}
			}
			&.active, &:hover{
				a{
					&:before{
						background: $primary-color;
						border-color: $primary-color;
					}
				}
			}
			&:last-child{
				margin-bottom: 0px;
			}
		}
	}
	.range_item{
		.ui-slider{
			height: 6px;
			border: none;
			background: #e8f0f2;
			.ui-slider-range{
				background: #e8f0f2;
			}
			.ui-slider-handle{
				height: 16px;
				width: 16px;
				border-radius: 50%;
				border: none;
				background: $primary-color;
				outline: none !important;
				box-shadow: none;
				top: -6px;
				cursor: pointer;
			}
		}
		label{
			display: inline-block;
			font-size: 14px;
			font-weight: normal;
			color: $text-color;
			font-family: $primary-font;
			margin-top: 15px;
		}
		input{
			display: inline-block;
			border: none;
			width: 100px;
			font-size: 14px;
			color: $text-color;
			font-family: $primary-font;
			margin-top: 9px;
			padding-left: 3px;
			@include placeholder{
				font-size: 14px;
				color: $text-color;
				font-family: $primary-font;
			}
		}
	}
}

.product_top_bar{
	background: #e8f0f2;
	clear: both;
	display: flex;
	padding: 10px 20px;
	.left_dorp{
		.sorting{
			display: inline-block;
			clear: none;
			border: 1px solid #eeeeee;
			border-radius: 0px;
			height: 40px;
			width: 210px;
			padding-left: 20px;
			span{
				font-size: 14px;
				font-family: $primary-font;
				color: #555555;
			}
			.list{
				width: 100%;
				border-radius: 0px;
				li{
					font-size: 14px;
					font-family: $primary-font;
					color: #555555;
				}
			}
			&:after{
				content: "\f0d7";
				font: normal normal normal 14px/1 FontAwesome;
				transform: rotate(0);
				border: none;
				color: #555555;
				margin-top: -6px;
				right: 20px;
			}
		}
		.show{
			clear: none;
			display: inline-block;
			clear: none;
			border: 1px solid #eeeeee;
			border-radius: 0px;
			height: 40px;
			width: 120px;
			padding-left: 20px;
			margin-left: 10px;
			span{
				font-size: 14px;
				font-family: $primary-font;
				color: #555555;
			}
			.list{
				width: 100%;
				border-radius: 0px;
				li{
					font-size: 14px;
					font-family: $primary-font;
					color: #555555;
				}
			}
			&:after{
				content: "\f0d7";
				font: normal normal normal 14px/1 FontAwesome;
				transform: rotate(0);
				border: none;
				color: #555555;
				margin-top: -6px;
				right: 20px;
			}
		}
	}
}
.cat_page{
	.pagination{
		margin-bottom: 0px;
		border-radius: 0px;
		li{
			a{
				height: 40px;
				width: 40px;
				border-radius: 0px;
				background: #fff;
				padding: 0px;
				text-align: center;
				line-height: 38px;
				border-color: #eeeeee;
				border-radius: 0px !important;
				font-size: 14px;
				color: $title-color;
				font-weight: normal;
				font-family: $primary-font;
			}
			&.blank{
				a{
					background: transparent;
					color: #cccccc;
					border: none;
				}
			}
			&:hover, &.active{
				a{
					background: $primary-color;
					color: #fff;
					border-color: $primary-color;
				}
			}
		}
	}
}
.most_p_withoutbox{
	padding-bottom: 120px;
}

/* End Product Sidebar Area css
============================================================================================ */

/* Single Product Area css
============================================================================================ */
.product_image_area{
	padding-top: 120px;
	
}
.s_product_inner{
	
}
.s_Product_carousel{
	.owl-dots{
		position: absolute;
		right: 20px;
		top: 50%;
		transform: translateY(-50%);
		div{
			height: 5px;
			width: 20px;
			background: #dddddd;
			margin: 5px;
			display: block;
			content: "";
			border-radius: 50px;
			&.active{
				width: 30px;
				@extend .gradient-bg;
			}
		}
	}
}

.s_product_text{
	margin-left: -15px;
	margin-top: 65px;
	@media (max-width: 575px) {
		margin-left: 0px;
	}
	h3{
		font-size: 24px;
		font-weight: 500;
		color: $title-color;
		margin-bottom: 10px;
	}
	h2{
		font-size: 24px;
		font-weight: bold;
		@extend .gradient-color;
		margin-bottom: 15px;
	}
	.list{
		li{
			margin-bottom: 5px;
			a{
				font-size: 14px;
				font-family: $primary-font;
				font-weight: normal;
				color: #555555;
				span{
					width: 90px;
					display: inline-block;
					&:hover{
						color: #555;
					}
				}
				&.active{
					@extend .gradient-color;
					span{
						color: #555;
						-webkit-text-fill-color: #555;
					}
				}
			}
			&:last-child{
				margin-bottom: 0px;
			}
		}
	}
	p{
		padding-top: 20px;
		border-top: 1px dotted #d5d5d5;
		margin-top: 20px;
		margin-bottom: 70px;
	}
	.card_area{
		.primary-btn{
			line-height: 38px;
			padding: 0px 38px;
			text-transform: uppercase;
			margin-right: 10px;
			border-radius: 5px;
		}
		.icon_btn{
			position: relative;
			height: 40px;
			width: 40px;
			line-height: 40px;
			text-align: center;
			background: #828bb2;
			border-radius: 50%;
			display: inline-block;
			color: #fff;
			@include transition();
			margin-right: 10px;
			z-index: 1;
			&:after{
				position: absolute;
				left: 0;
				top: 0;
				height: 100%;
				width: 100%;
				content: "";
				@extend .gradient-bg;
				border-radius: 50%;
				opacity: 0;
				visibility: hidden;
				z-index: -1;
			}
			&:hover{
				&:after{
					opacity: 1;
					visibility: visible;
				}
			}
		}
	}
}
.product_count{
	display: inline-block;
	position: relative;
	margin-bottom: 24px;
	label{
		font-size: 14px;
		color: $text-color;
		font-family: $primary-font;
		font-weight: normal;
		padding-right: 10px;
	}
	input{
		width: 76px;
		border: 1px solid #eeeeee;
		border-radius: 3px;
		padding-left: 10px;
	}
	button{
		display: block;
		border: none;
		background: transparent;
		box-shadow: none;
		cursor: pointer;
		position: absolute;
		right: 0px;
		font-size: 14px;
		color: #cccccc;
		@include transition;
		&:hover{
			color: $title-color;
		}
	}
	.increase{
		top: -4px;
	}
	.reduced{
		bottom: -8px;
	}
	
}



.product_description_area{
	padding-bottom: 120px;
	margin-top: 60px;
	.nav.nav-tabs{
		background: #e8f0f2;
		text-align: center;
		display: block;
		border: none;
		padding: 10px 0px;
		li{
			display: inline-block;
			margin-right: 7px;
			&:last-child{
				margin-right: 0px;
				@media (max-width: 441px) {
					margin-top: 15px;
				}
			}
			a{
				padding: 0px;
				border: none;
				line-height: 38px;
				background: #fff;
				border: 1px solid #eeeeee;
				border-radius: 0px;
				padding: 0px 30px;
				color: $title-color;
				font-size: 13px;
				font-weight: normal;
				@media (max-width: 570px) {
					padding: 0 15px;
				}
				&.active{
					@extend .gradient-bg;
					color: #fff;
					border: 1px solid transparent;
				}
			}
		}
	}
	.tab-content{
		border-left: 1px solid #eee;
		border-right: 1px solid #eee;
		border-bottom: 1px solid #eee;
		padding: 30px;
		.total_rate{
			.box_total{
				background: #e8f0f2;
				text-align: center;
				padding-top: 20px;
				padding-bottom: 20px;
				h4{
					@extend .gradient-color;
					font-size: 48px;
					font-weight: bold;
				}
				h5{
					color: $title-color;
					margin-bottom: 0px;
					font-size: 24px;
				}
				h6{
					color: $title-color;
					margin-bottom: 0px;
					font-size: 14px;
					color: $text-color;
					font-weight: normal;
				}
			}
			.rating_list{
				margin-bottom: 30px;
				h3{
					font-size: 18px;
					color: $title-color;
					font-family: $primary-font;
					font-weight: 500;
					margin-bottom: 10px;
				}
				.list{
					li{
						a{
							font-size: 14px;
							color: $text-color;
							i{
								color: #fbd600;
							}
						}
						&:nth-child{
							a{
								i{
									&:last-child{
										color: #eeeeee;
									}
								}
							}
						}
					}
				}
			}
		}
		.table{
			margin-bottom: 0px;
			tbody{
				tr{
					td{
						padding-left: 65px;
						padding-right: 65px;
						padding-top: 14px;
						padding-bottom: 14px;
						h5{
							font-size: 14px;
							font-family: $primary-font;
							font-weight: normal;
							color: $text-color;
							margin-bottom: 0px;
							white-space: nowrap;
						}
					}
					&:first-child{
						td{
							border-top: 0px;
						}
					}
				}
			}
		}
	}
}
.review_list{
	
}
.review_item{
	margin-bottom: 15px;
	&:last-child{
		margin-bottom: 0px;
	}
	.media{
		position: relative;
		.d-flex{
			padding-right: 15px;
		}
		.media-body{
			vertical-align: middle;
			align-self: center;
			h4{
				margin-bottom: 0px;
				font-size: 14px;
				color: $title-color;
				font-family: $primary-font;
				margin-bottom: 8px;
			}
			i{
				color: #fbd600;
			}
			h5{
				font-size: 13px;
				font-weight: normal;
				color: $text-color;
			}
			.reply_btn{
				border: 1px solid #e0e0e0;
				padding: 0px 28px;
				display: inline-block;
				line-height: 32px;
				border-radius: 16px;
				font-size: 14px;
				font-family: $primary-font;
				color: $title-color;
				position: absolute;
				right: 0px;
				top: 14px;
				@icnlude transition;
				&:hover{
					background: $primary-color;
					border-color: $primary-color;
					color: #fff;
				}
			}
		}

	}
	p{
		padding-top: 10px;
		margin-bottom: 0px;
	}
	&.reply{
		padding-left: 28px;
	}
}

.review_box{
	h4{
		font-size: 24px;
		color: $title-color;
		margin-bottom: 20px;
	}
	p{
		margin-bottom: 0px;
		display: inline-block;
	}
	.list{
		display: inline-block;
		padding-left: 10px;
		padding-right: 10px;
		li{
			display: inline-block;
			a{
				display: inline-block;
				color: #fbd600;
			}
		}
	}
	.contact_form{
		margin-top: 15px;
	}
	.primary-btn{
		line-height: 38px!important;
		padding: 0px 38px;
		text-transform: uppercase;
		margin-right: 10px;
		border-radius: 5px;
		border: none;
	}
}

.comment_list{
	.review_item{
		
	}
}


/* End Single Product Area css
============================================================================================ */

/* Cart Area css
============================================================================================ */
.cart_area{
	padding-top: 100px;
	padding-bottom: 100px;
}
.cart_inner{
	.table{
		thead{
			tr{
				th{
					border-top: 0px;
					font-size: 14px;
					font-weight: 500;
					font-family: $primary-font;
					color: $text-color;
					border-bottom: 0px !important; 
				}
			}
		}
		tbody{
			tr{
				td{
					padding-top: 30px;
					padding-bottom: 30px;
					vertical-align: middle;
					align-self: center;
					.media{
						.d-flex{
							padding-right: 30px;
							img{
								border: 1px solid #eeeeee;
								border-radius: 3px;
							}
						}
						.media-body{
							vertical-align: middle;
							align-self: center;
							p{
								margin-bottom: 0px;
							}
						}
					}
					h5{
						font-size: 14px;
						color: $title-color;
						font-family: $primary-font;
						margin-bottom: 0px;
					}
					.product_count{
						margin-bottom: 0px;
						input{
							width: 100px;
							padding-left: 30px;
							height: 40px;
							outline: none;
							box-shadow: none;
						}
						.increase{
							top: -2px;
							&:before{
								content: "";
								height: 40px;
								width: 1px;
								position: absolute;
								left: -3px;
								top: 0px;
								background: #eeeeee;
							}
							&:after{
								content: "";
								height: 1px;
								width: 30px;
								position: absolute;
								left: -3px;
								top: 22px;
								background: #eeeeee;
							}
						}
						.reduced{
							bottom: -6px;
						}
					}
				}
				&.bottom_button{
					.gray_btn{
						line-height: 38px;
						background: #e8f0f2;
						border: 1px solid #eeeeee;
						border-radius: 3px;
						padding: 0px 40px;
						display: inline-block;
						color: $title-color;
						text-transform: uppercase;
						font-weight: 500;
					}
					td{
						&:last-child{
							width: 60px;
						}
						.cupon_text{
							margin-left: -446px; 
							input{
								width: 200px;
								padding: 0px 15px;
								border-radius: 3px;
								border: 1px solid #eeeeee;
								height: 40px;
								font-size: 14px;
								color: #cccccc;
								font-family: $primary-font;
								font-weight: normal;
								margin-right: -3px;
								outline: none;
								box-shadow: none;
								@include placeholder{
									font-size: 14px;
									color: #cccccc;
									font-family: $primary-font;
									font-weight: normal;
								}
							}
							.primary-btn{
								height: 40px;
								line-height: 38px;
								text-transform: uppercase;
								padding: 0px 38px;
								margin-right: -3px;
								border-radius: 0;
							}
							.gray_btn{
								padding: 0px 40px;
							}
						}
					}
				}
				&.shipping_area{
					td{
						&:nth-child(3){
							vertical-align: top;
						}
					}
					.shipping_box{
						margin-left: -250px;
						text-align: right;
						.list{
							li{
								margin-bottom: 12px;
								&:last-child{
									margin-bottom: 0px;
								}
								a{
									padding-right: 30px;
									font-size: 14px;
									color: $text-color;
									position: relative;
									&:before{
										content: "";
										height: 16px;
										width: 16px;
										border: 1px solid #cdcdcd;
										display: inline-block;
										border-radius: 50%;
										position: absolute;
										right: 0px;
										top: 50%;
										transform: translateY(-50%);
									}
									&:after{
										content: "";
										height: 10px;
										width: 10px;
										border-radius: 50%;
										background: $primary-color;
										display: inline-block;
										position: absolute;
										right: 3px;
										top: 50%;
										transform: translateY(-50%);
										opacity: 0;
									}
								}
								&.active{
									a{
										&:after{
											opacity: 1;
										}
									}
								}
							}
						}
						h6{
							font-size: 14px;
							font-weight: normal;
							color: $title-color;
							font-family: $primary-font;
							margin-top: 20px;
							margin-bottom: 20px;
							i{
								color: $text-color;
								padding-left: 5px;
							}
						}
						.shipping_select{
							display: block;
							width: 100%;
							background: #e8f0f2;
							border: 1px solid #eeeeee;
							height: 40px;
							margin-bottom: 20px;
							.list{
								width: 100%;
								border-radius: 0px;
								li{
									font-size: 14px;
									font-weight: normal;
									color: $text-color;
								}
							}
						}
						input{
							height: 40px;
							outline: none;
							border: 1px solid #eeeeee;
							background: #e8f0f2;
							width: 100%;
							padding: 0px 15px;
							margin-bottom: 20px;
						}
					}
				}
				&.out_button_area{
					.checkout_btn_inner{
						margin-left: -388px;
						.primary-btn{
							height: 40px;
							padding: 0px 30px;
							line-height: 38px;
							text-transform: uppercase;
							border-radius: 0;
							&:hover{
								&:before{
									left: 240px;
								}
							}
						}
					}
				}
			}
		}
	}
}
.gray_btn{
	line-height: 38px;
	background: #e8f0f2;
	border: 1px solid #eeeeee;
	border-radius: 3px;
	padding: 0px 40px;
	display: inline-block;
	color: $title-color;
	text-transform: uppercase;
	font-weight: 500;
}
/* End Cart Area css
============================================================================================ */

/* End Cart Area css
============================================================================================ */
.checkout_area{
	
}
.check_title{
	h2{
		font-size: 14px;
		font-weight: normal;
		font-family: $primary-font;
		background: #e8f0f2;
		line-height: 40px!important;
		padding-left: 30px;
		margin-bottom: 0px;
		a{
			color: #c5322d;
			text-decoration: underline;
		}
	}
}
.returning_customer{
	p{
		margin-top: 15px;
		padding-left: 30px;
		margin-bottom: 25px;
	}
	.contact_form{
		max-width: 710px;
		margin-left: 15px;
		.form-group{
			margin-bottom: 20px;
			input{
				border: 1px solid #eeeeee;
				height: 40px;
				border-radius: 3px;
				font-size: 14px;
				font-family: $primary-font;
				color: $text-color;
				font-weight: normal;
				@include placeholder{
					font-size: 14px;
					font-family: $primary-font;
					color: $text-color;
					font-weight: normal;
				}
			}
			.primary-btn{
				line-height: 38px;
				padding: 0px 38px;
				text-transform: uppercase;
				margin-right: 10px;
				border-radius: 2px;
				border: none;
			}
			.creat_account{
				display: inline-block;
				margin-left: 15px;
				input{
					height: auto;
					margin-right: 10px;
				}
			}
			.lost_pass{
				display: block;
				margin-top: 20px;
				font-size: 14px;
				font-family: $primary-font;
				color: $text-color;
				font-weight: normal;
			}
		}
	}
}



.p_star {
    display: inline-block;
    position: relative;
}

.p_star input {
    background: #fff;
}

//.p_star input:valid + .placeholder {
//    display: none;
//}
.p_star input:focus + .placeholder{
    display: none;
}
.p_star .placeholder {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 30px;
    z-index: 1;
}

.p_star .placeholder::before {
    content: attr(data-placeholder);
    color: #777;
}

.p_star .placeholder::after {
    content: " *";
    color: tomato;
}



.cupon_area{
	margin-bottom: 40px;
	input{
		margin-left: 30px;
		max-width: 730px;
		width: 100%;
		display: block;
		height: 40px;
		border-radius: 3px;
		padding: 0px 15px;
		border: 1px solid #eeeeee;
		outline: none;
		box-shadow: none;
		margin-top: 20px;
		margin-bottom: 20px;
	}
	.tp_btn{
		margin-left: 30px;
	}
}
.tp_btn{
	border: 1px solid #eeeeee;
	display: inline-block;
	line-height: 38px;
	padding: 0px 40px;
	color: $title-color;
	text-transform: uppercase;
	font-family: $primary-font;
	font-weight: 500;
	border-radius: 3px;
	&:hover{
		background: $primary-color;
		color: #fff;
		border-color: $primary-color;
	}
}


.billing_details{
	h3{
		font-size: 18px;
		color: $title-color;
		border-bottom: 1px solid #eee;
		padding-bottom: 10px;
		margin-bottom: 30px;
	}
	.contact_form{
		.form-group{
			margin-bottom: 20px;
			input{
				border-radius: 3px;
				color: $text-color;
				@include placeholder{
					color: $text-color;
				}
			}
			textarea{
				height: 150px;
				border-radius: 3px;
				margin-top: 15px;
			}
			.country_select{
				width: 100%;
				span{
					color: $text-color;
				}
				.list{
					width: 100%;
					border-radius: 0px;
					li{
						font-size: 14px;
						font-family: $primary-font;
						font-weight: normal;
					}
				}
			}
			.creat_account{
				#f-option2{
					margin-right: 5px;
				}
				#f-option3{
					margin-right: 5px;
				}
				a{
					color: $primary-color;
				}
			}
		}
	}
}
.order_box{
	background: #e5ecee;
	padding: 30px;
	h2{
		border-bottom: 1px solid #dddddd;
		font-size: 18px;
		font-weight: 500;
		color: $title-color;
		padding-bottom: 15px;
	}
	.list{
		li{
			a{
				font-size: 14px;
				color: $text-color;
				font-weight: normal;
				border-bottom: 1px solid #eeeeee;
				display: block;
				line-height: 42px;
				span{
					float: right;
				}
				.middle{
					float: none;
					width: 50px;
					text-align: right;
					display: inline-block;
					margin-left: 30px;
					color: $title-color;
				}
			}
			&:nth-child(4){
				a{
					.middle{
						margin-left: 48px;
					}
				}
			}
		}
	}
	.list_2{
		li{
			a{
				text-transform: uppercase;
				color: $title-color;
				font-weight: 500;
				span{
					color: $text-color;
					text-transform: capitalize;
				}
			}
			&:last-child{
				a{
					span{
						color: $title-color;
					}
				}
			}
		}
	}
	.payment_item{
		h4{
			font-size: 14px;
			text-transform: uppercase;
			color: $title-color;
			font-weight: 500;
			padding-left: 22px;
			position: relative;
			margin-bottom: 15px;
			margin-top: 15px;
			&:before{
				content: "";
				width: 14px;
				height: 14px;
				border-radius: 50%;
				border: 1px solid #cdcdcd;
				background: #fff;
				display: block;
				position: absolute;
				left: 0px;
				top: 50%;
				transform: translateY(-50%);
			}
			&:after{
				content: "";
				height: 4px;
				width: 4px;
				background: #fff;
				border-radius: 50%;
				display: block;
				position: absolute;
				left: 5px;
				top: 8px;
			}
			img{
				padding-left: 60px;
			}
		}
		p{
			background: #fff;
			padding: 20px;
		}
		&.active{
			h4{
				&:before{
					background: $primary-color;
					border-color: $primary-color;
				}
			}
		}
	}
	.creat_account{
		margin-bottom: 15px;
		label{
			padding-left: 10px;
			font-size: 12px;
			color: $title-color;
		}
		a{
			color: #c5322d;
		}
	}
	.primary-btn{
		display: block;
		line-height: 38px;
		text-transform: uppercase;
		text-align: center;
		border-radius: 2px;
		&:hover{
			&:before{
				left: 310px;
			}
		}
	}
}


.order_details{
	.title_confirmation{
		text-align: center;
		color: #28d500;
		font-size: 18px;
		margin-bottom: 80px;
	}
}
.order_d_inner{
	.details_item{
		h4{
			border-bottom: 1px solid #eee;
			padding-bottom: 12px;
			font-size: 18px;
			color: $title-color;
			padding-left: 18px;
		}
		.list{
			padding-left: 18px;
			@media (min-width: 767px) {
				margin-bottom: 30px;
			}
			li{
				margin-bottom: 8px;
				&:last-child{
					margin-bottom: 0px;
				}
				a{
					font-size: 14px;
					color: $title-color;
					font-family: $primary-font;
					span{
						width: 145px;
						display: inline-block;
						color: $text-color;
					}
				}
			}
		}
	}
}
.order_details_table{
	background: #e5ecee;
	padding: 30px;
	margin-top: 75px;
	@media (min-width: 767px) {
		margin-top: 45px;
	}
	h2{
		color: $title-color;
		font-size: 18px;
		padding-bottom: 15px;
	}
	.table{
		margin-bottom: 0px;
		thead{
			tr{
				th{
					border-bottom: 1px solid #ddd;
					font-size: 14px;
					font-family: $primary-font;
					font-weight: normal;
				}
			}
		}
		tbody{
			tr{
				td{
					p{
						margin-bottom: 0px;
					}
					h5{
						color: $title-color;
						margin-bottom: 0px;
					}
					h4{
						font-size: 14px;
						text-transform: uppercase;
						margin-bottom: 0px;
						color: $title-color;
					}
				}
				&:last-child{
					td{
						border-top: 1px solid #ddd;
						p{
							color: $title-color;
						}
					}
				}
			}
		}
	}
}
/* End Cart Area css
============================================================================================ */

/* Login form Area css
============================================================================================ */
.login_box_area{
	.login_box_img{
		margin-right: -30px;
		position: relative;
		img{
			width: 100%;
		}
		&:before{
			position: absolute;
			left: 0;
			top: 0;
			height: 100%;
			width: 100%;
			content: "";
			background: #000;
			opacity: .5;
		}
		.hover{
			position: absolute;
			top: 50%;
			left: 0px;
			text-align: center;
			width: 100%;
			transform: translateY(-50%);
			h4{
				font-size: 24px;
				color: #fff;
				margin-bottom: 15px;
			}
			p{
				max-width: 380px;
				margin: 0px auto 25px;
				color: #fff;
			}
			.primary-btn{
				border-radius: 0px;
				line-height: 38px;
				text-transform: uppercase;
				&:hover{
					&:before{
						left: 260px;
					}
				}
			}
		}
	}
}
.login_form_inner{
	box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.07);
	height: 100%;
	text-align: center;
	padding-top: 115px;
	h3{
		color: $title-color;
		text-transform: uppercase;
		font-size: 18px;
		margin-bottom: 30px;
	}
	.login_form{
		max-width: 385px;
		margin: auto;
		.form-group{
			
		}
	}
	&.reg_form{
		padding-top: 60px;
		h3{
			margin-bottom: 70px;
		}
	}
}
.login_form{
	.form-group{
		input{
			height: 54px;
			border: none;
			border-bottom: 1px solid #cccccc;
			border-radius: 0px;
			outline: none;
			box-shadow: none;
			@include placeholder{
				
			}
		}
		.creat_account{
			margin-top: 10px;
			margin-bottom: 15px;
			text-align: left;
			input{
				height: auto;
				margin-right: 10px;
			}
		}
		.primary-btn{
			display: block;
			border-radius: 0px;
			line-height: 38px;
			width: 100%;
			text-transform: uppercase;
			border: none;
			&:hover{
				&:before{
					left: 370px;
				}
			}
		}
		a{
			font-size: 14px;
			color: $text-color;
			font-family: $primary-font;
			margin-top: 20px;
			display: block;
		}
	}
}
/* End Login form Area css
============================================================================================ */

/* Tracking Form Area css
============================================================================================ */
.tracking_box_area{
	.tracking_box_inner{
		p{
			max-width: 870px;
			color: $title-color;
			margin-bottom: 25px;
		}
		.tracking_form{
			max-width: 645px;
			.form-group{
				input{
					height: 40px;
					border: 1px solid #eee;
					padding: 0px 15px;
					outline: none;
					box-shadow: none;
					border-radius: 0px;
					font-size: 14px;
					color: $text-color;
					font-family: $primary-font;
					font-weight: normal;
					@include placeholder{
						font-size: 14px;
						color: $text-color;
						font-family: $primary-font;
						font-weight: normal;
					}
				}
				&:last-child{
					margin-bottom: 0px;
				}
				.submit_btn{
					text-transform: uppercase;
				}
			}
		}
	}
}
/* End Tracking Form Area css
============================================================================================ */



.radion_btn input[type=radio]{
    position: absolute;
    visibility: hidden;
}
.radion_btn{
    position: relative;
	margin-top: 10px;
	margin-bottom: 15px;
	img{
		position: absolute;
		right: 40px;
		top: 0px;
	}
}
.radion_btn label{
    display: block;
    position: relative;
    font-weight: 300;
    font-size: 1.35em;
    padding: 0px 25px 21px 25px;
    height: 14px;
    z-index: 9;
    cursor: pointer;
    -webkit-transition: all 0.25s linear;
    font-family: $primary-font;
	font-weight: 500;
    color: $title-color;
    font-size: 13px;
    letter-spacing: .25px;
    text-transform: uppercase;
}
.radion_btn .check{
    display: block;
    position: absolute;
    border: 1px solid #cdcdcd;
    border-radius: 100%;
    height: 14px;
    width: 14px;
    top: 5px;
    left: 0px;
	background: #fff;
    z-index: 5;
    transition: border .25s linear;
    -webkit-transition: border .25s linear;
}

.radion_btn .check::before {
    display: block;
    position: absolute;
    content: '';
    border-radius: 100%;
    height: 4px;
    width: 4px;
    top: 4px; 
    left: 4px;
    margin: auto;
    transition: background 0.25s linear;
    -webkit-transition: background 0.25s linear;
}

.radion_btn input[type=radio]:checked ~ .check {
    border: 1px solid $primary-color;
	background: $primary-color;
}

.radion_btn input[type=radio]:checked ~ .check::before{
    background: #fff;
}

.radion_btn input[type=radio]:checked ~ label{
    color: #000;
}
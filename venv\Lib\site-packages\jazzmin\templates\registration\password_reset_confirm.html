{% extends "registration/base.html" %}

{% load i18n %}

{% block content %}
    {% if validlink %}
        <p class="login-box-msg">
            {% trans "Please enter your new password twice so we can verify you typed it in correctly." %}
        </p>
        <form method="post">{% csrf_token %}
            {% if form.errors %}
                {% if form.new_password1.errors %}
                    <div class="callout callout-danger">
                        <p>{{ form.new_password1.label }}: {{ form.new_password1.errors|join:', ' }}</p>
                    </div>
                {% endif %}
                {% if form.new_password2.errors %}
                    <div class="callout callout-danger">
                        <p>{{ form.new_password2.label }}: {{ form.new_password2.errors|join:', ' }}</p>
                    </div>
                {% endif %}
                {% if form.non_field_errors %}
                    <div class="callout callout-danger">
                        {% for error in form.non_field_errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endif %}
            <input class="hidden" autocomplete="username" value="{{ form.user.get_username }}">
            <div class="input-group mb-3">
                <input type="password" name="new_password1" class="form-control" placeholder="{{ form.new_password1.label }}" required>
                <div class="input-group-append">
                    <div class="input-group-text">
                        <span class="fas fa-lock"></span>
                    </div>
                </div>
            </div>
            <div class="input-group mb-3">
                <input type="password" name="new_password2" class="form-control" placeholder="{{ form.new_password2.label }}" required>
                <div class="input-group-append">
                    <div class="input-group-text">
                        <span class="fas fa-lock"></span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <button type="submit" class="btn {{ jazzmin_ui.button_classes.primary }} btn-block">
                        {% trans 'Change my password' %}
                    </button>
                </div>
            </div>
        </form>
    {% else %}
        <p>
            {% trans "The password reset link was invalid, possibly because it has already been used.  Please request a new password reset." %}
        </p>
    {% endif %}
{% endblock %}

{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_list jazzmin %}

{% block extrastyle %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'vendor/select2/css/select2.min.css' %}">

    {% if cl.formset or action_form %}
        <script type="text/javascript" src="{% url 'admin:jsi18n' %}"></script>
    {% endif %}
    {{ media.css }}
    {% if not actions_on_top and not actions_on_bottom %}
        <style>
            #changelist table thead th:first-child {width: inherit}
        </style>
    {% endif %}
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    {{ media.js }}
{% endblock %}

{% block bodyclass %}{{ block.super }} app-{{ opts.app_label }} model-{{ opts.model_name }} change-list{% endblock %}

{% block breadcrumbs %}
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a></li>
        <li class="breadcrumb-item active">{{ cl.opts.verbose_name_plural|capfirst }}</li>
    </ol>
{% endblock %}

{% block content_title %} {{ cl.opts.verbose_name_plural|capfirst }} {% endblock %}

{% block coltype %}flex{% endblock %}

    {% block page_actions %}
        <div class="col-12 col-md-auto d-flex align-items-center justify-content-end page-actions">
            {% block object-tools %}
                {% block object-tools-items %}
                    {% change_list_object_tools %}
                {% endblock %}
            {% endblock %}
        </div>
    {% endblock %}


{% block content %}

    {% block date_hierarchy %}{% if cl.date_hierarchy %}{% date_hierarchy cl %}{% endif %}{% endblock %}

    {% block search %}
        {% search_form cl %}
    {% endblock %}

    <div class="col-12">
        <div>
            <form id="changelist-form" method="post"{% if cl.formset and cl.formset.is_multipart %}enctype="multipart/form-data"{% endif %} novalidate>{% csrf_token %}
                <div id="content-main">
                    {% if cl.formset and cl.formset.errors %}
                    <div class="alert alert-warning" role="alert">
                        {% if cl.formset.total_error_count == 1 %}
                            {% trans "Please correct the error below." %}
                        {% else %}
                            {% trans "Please correct the errors below." %}
                        {% endif %}
                    </div>
                    {{ cl.formset.non_form_errors }}
                    {% endif %}
                    <div class="module{% if cl.has_filters %} filtered{% endif %}" id="changelist">
                    <div class="row">
                        <div class="col-12">
                            {% if cl.formset %}
                                <div>{{ cl.formset.management_form }}</div>
                            {% endif %}

                            {% block result_list %}
                                <div class="change-list-actions row pb-3">
                                    <div class="col-12">
                                        {% if action_form and actions_on_top and cl.show_admin_actions %}
                                            {% admin_actions %}
                                        {% endif %}
                                    </div>
                                </div>
                                {% result_list cl %}
                                {% if action_form and actions_on_bottom and cl.show_admin_actions %}
                                    <div class="row">
                                        <div class="col-12">
                                            {% admin_actions %}
                                        </div>
                                    </div>
                                {% endif %}
                            {% endblock %}
                        </div>
                    </div>
                    <div class="row">
                        {% block pagination %}{% pagination cl %}{% endblock %}
                    </div>
                </div>
                </div>
            </form>
        </div>

        <br class="clear"/>
    </div>
{% endblock %}

{% block extrajs %}
    {{  block.super }}
    <script type="text/javascript" src="{% static 'vendor/select2/js/select2.min.js' %}"></script>
    <script>
        {% comment %} set filterInputLength default and custom values {% endcomment %}
        window.filterInputLengthDefault = 0;
        window.filterInputLength = {
            {% for k,v in cl.model_admin.filter_input_length.items %}
                '{{ k }}': {{ v }},
            {% endfor %}
        }
    </script>
    <script type="text/javascript" src="{% static 'jazzmin/js/change_list.js' %}"></script>
{% endblock %}

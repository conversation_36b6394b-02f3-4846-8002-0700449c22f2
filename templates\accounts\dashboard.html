{% extends "base.html" %}
{% load static %}

{% block content %}
<!-- Dashboard Styling -->
<style>
.dashboard-container {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 60px 0;
    margin-top: 30px;
}

.dashboard-header {
    background: linear-gradient(135deg, #ffba00, #ff6c00);
    color: white;
    padding: 50px 0;
    margin-bottom: 50px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(255, 186, 0, 0.2);
}

.dashboard-header h1 {
    font-size: 3.2rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-header p {
    font-size: 1.3rem;
    opacity: 0.95;
    margin: 0;
    font-weight: 400;
}

.dashboard-stats {
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
    border-left: 4px solid #ffba00;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    color: #ffba00;
    margin-bottom: 15px;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.stat-card .stat-label {
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.dashboard-section {
    background: white;
    border-radius: 10px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f1f1f1;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-title i {
    color: #ffba00;
}

.order-card {
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.order-card:hover {
    border-color: #ffba00;
    box-shadow: 0 3px 10px rgba(255, 186, 0, 0.1);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.order-number {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

.order-status {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-processing {
    background: #cce7ff;
    color: #004085;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.status-delivered {
    background: #d4edda;
    color: #155724;
}

.status-shipped {
    background: #cce7ff;
    color: #004085;
}

.no-orders {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-orders i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 20px;
}

.no-orders h4 {
    color: #333;
    margin-bottom: 15px;
}

.no-orders p {
    margin-bottom: 25px;
}

.order-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.order-detail {
    text-align: center;
}

.order-detail-label {
    font-size: 0.8rem;
    color: #666;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.order-detail-value {
    font-weight: 600;
    color: #333;
}

.order-items {
    background: #f8f9fa;
    border-radius: 5px;
    padding: 15px;
    margin-top: 15px;
}

.order-items h6 {
    margin-bottom: 10px;
    color: #333;
}

.item-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.item-list li {
    padding: 5px 0;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
}

.item-list li:last-child {
    border-bottom: none;
}

.btn-primary-custom {
    background: linear-gradient(135deg, #ffba00, #ff6c00);
    border: none;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 186, 0, 0.3);
    color: white;
    text-decoration: none;
}

.btn-primary-custom i {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #333;
    padding: 15px;
}

.table td {
    padding: 15px;
    vertical-align: middle;
}

.badge {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 40px 0;
        margin-top: 20px;
    }

    .dashboard-header {
        padding: 40px 20px;
        margin-bottom: 40px;
        border-radius: 12px;
    }

    .dashboard-header h1 {
        font-size: 2.5rem;
    }

    .dashboard-header p {
        font-size: 1.1rem;
    }

    .order-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .order-details {
        grid-template-columns: 1fr 1fr;
    }

    .btn-primary-custom {
        padding: 12px 15px;
        margin-bottom: 10px;
    }

    .table-responsive {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .dashboard-header h1 {
        font-size: 2rem;
    }

    .dashboard-header p {
        font-size: 1rem;
    }
}
</style>

<!-- Dashboard Content -->
<div class="dashboard-container">
    <div class="container">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <h1>Welcome back, {{ user.first_name|default:user.username }}!</h1>
            <p>Here's an overview of your account and recent activity</p>
        </div>

        <!-- Dashboard Stats -->
        <div class="dashboard-stats">
            <div class="row">
                <div class="col-md-3 col-sm-6 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ti-package"></i>
                        </div>
                        <div class="stat-number">{{ total_orders }}</div>
                        <div class="stat-label">Total Orders</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ti-check-box"></i>
                        </div>
                        <div class="stat-number">{{ completed_orders }}</div>
                        <div class="stat-label">Completed</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ti-money"></i>
                        </div>
                        <div class="stat-number">${{ total_spent|floatformat:2 }}</div>
                        <div class="stat-label">Total Spent</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ti-bag"></i>
                        </div>
                        <div class="stat-number">{{ favorites_count }}</div>
                        <div class="stat-label">Cart</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Orders Section -->
        <div class="dashboard-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="ti-package"></i>
                    Recent Orders
                </h2>
            </div>

            {% if recent_orders %}
                {% for order in recent_orders %}
                <div class="order-card">
                    <div class="order-header">
                        <div class="order-number">Order #{{ order.order_number }}</div>
                        <div class="order-status status-{{ order.status }}">{{ order.get_status_display }}</div>
                    </div>
                    <div class="order-details">
                        <div class="order-detail">
                            <div class="order-detail-label">Date</div>
                            <div class="order-detail-value">{{ order.created_at|date:"M d, Y" }}</div>
                        </div>
                        <div class="order-detail">
                            <div class="order-detail-label">Items</div>
                            <div class="order-detail-value">{{ order.items.count }} Item{{ order.items.count|pluralize }}</div>
                        </div>
                        <div class="order-detail">
                            <div class="order-detail-label">Total</div>
                            <div class="order-detail-value">${{ order.order_total|floatformat:2 }}</div>
                        </div>
                        <div class="order-detail">
                            <div class="order-detail-label">Payment</div>
                            <div class="order-detail-value">{{ order.get_payment_status_display }}</div>
                        </div>
                    </div>
                    <div class="order-items">
                        <h6>Items Ordered:</h6>
                        <ul class="item-list">
                            {% for item in order.items.all %}
                            <li>
                                <span>{{ item.product.product_name }}</span>
                                <span>${{ item.product_price|floatformat:2 }} x {{ item.quantity }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="no-orders">
                    <i class="ti-package"></i>
                    <h4>No Orders Yet</h4>
                    <p>You haven't placed any orders yet. Start shopping to see your orders here!</p>
                    <a href="{% url 'store' %}" class="btn-primary-custom">Start Shopping</a>
                </div>
            {% endif %}


        </div>

        <!-- Payment History Section -->
        <div class="dashboard-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="ti-credit-card"></i>
                    Payment History
                </h2>
            </div>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead style="background: #f8f9fa;">
                        <tr>
                            <th>Transaction ID</th>
                            <th>Date</th>
                            <th>Order</th>
                            <th>Method</th>
                            <th>Amount</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if payments %}
                            {% for payment in payments %}
                            <tr>
                                <td><strong>{{ payment.payment_id }}</strong></td>
                                <td>{{ payment.created_at|date:"M d, Y" }}</td>
                                <td>{{ payment.order.order_number }}</td>
                                <td>
                                    <span class="badge" style="background:
                                        {% if payment.payment_method == 'credit_card' %}#007bff
                                        {% elif payment.payment_method == 'paypal' %}#28a745
                                        {% elif payment.payment_method == 'bank_transfer' %}#ffc107
                                        {% else %}#6c757d{% endif %};
                                        color: {% if payment.payment_method == 'bank_transfer' %}black{% else %}white{% endif %};">
                                        {{ payment.get_payment_method_display }}
                                    </span>
                                </td>
                                <td><strong>${{ payment.amount_paid|floatformat:2 }}</strong></td>
                                <td>
                                    <span class="order-status status-{{ payment.status }}">{{ payment.get_status_display }}</span>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="6" class="text-center">
                                    <p>No payment history available.</p>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="dashboard-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="ti-settings"></i>
                    Quick Actions
                </h2>
            </div>
            <div class="row">
                <div class="col-md-4 col-sm-6 mb-3">
                    <a href="{% url 'store' %}" class="btn-primary-custom d-block text-center">
                        <i class="ti-shopping-cart"></i><br>
                        Continue Shopping
                    </a>
                </div>
                <div class="col-md-4 col-sm-6 mb-3">
                    <a href="#" class="btn-primary-custom d-block text-center">
                        <i class="ti-user"></i><br>
                        Edit Profile
                    </a>
                </div>
                <div class="col-md-4 col-sm-6 mb-3">
                    <a href="#" class="btn-primary-custom d-block text-center">
                        <i class="ti-headphone-alt"></i><br>
                        Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock content %}
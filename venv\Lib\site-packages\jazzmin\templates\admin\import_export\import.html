{% extends "admin/import_export/base.html" %}
{% load i18n admin_urls import_export_tags static jazzmin %}
{% get_jazzmin_ui_tweaks as jazzmin_ui %}

{% block extrastyle %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{% static "import_export/import.css" %}"/>
    <link rel="stylesheet" href="{% static 'vendor/select2/css/select2.min.css' %}">
{% endblock %}

{% block breadcrumbs_last %}
    {% trans "Import" %}
{% endblock %}

{% block content %}
    <div class="col-12">
        {% if confirm_form %}
            <form action="{% url opts|admin_urlname:"process_import" %}" method="POST">
                {% csrf_token %}
                <div class="row">
                    <div class="col-12 col-lg-9">
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    {% trans 'Confirm Import' %}
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="col-12 col-lg-9">
                                    {{ confirm_form.as_p }}
                                    <p>
                                        {% trans "Below is a preview of data to be imported. If you are satisfied with the results, click 'Confirm import'" %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-lg-3">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-edit"></i>
                                    {% trans 'Actions' %}
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <input type="submit" class="btn {{ jazzmin_ui.button_classes.success }} form-control" name="confirm" value="{% trans "Confirm import" %}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        {% else %}
            <form action="" method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="row">
                    <div class="col-12 col-lg-9">
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    {% trans 'Confirm Import' %}
                                </div>
                            </div>
                            <div class="card-body">
                                {% for field in form %}
                                    <div class="form-group field-name">
                                        <div class="row">
                                        {{ field.errors }}
                                            <div class="col-sm-2 text-left">
                                                {{ field.label_tag }}
                                            </div>
                                            <div class="col-sm-10 text-left">
                                                {{ field }}
                                            </div>

                                            <div class="help-block red">
                                                {% if field.field.help_text %}
                                                    <img src="{% static "admin/img/icon-unknown.svg" %}" class="help help-tooltip" width="10" height="10"  alt="({{ field.field.help_text|striptags }})" title="{{ field.field.help_text|striptags }}">
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                                <div class="col-12 col-lg-9">
                                    <p>
                                        <b>{% trans "This importer will import the following fields: " %}</b>
                                        <ul>
                                        {% for field in fields %}
                                            <li>{{ field }}</li>
                                        {% endfor %}
                                        </ul>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 col-lg-3">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-edit"></i>
                                    {% trans 'Actions' %}
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <input type="submit" class="btn {{ jazzmin_ui.button_classes.success }} form-control" name="confirm" value="{% trans "Submit" %}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        {% endif %}

    {% if result %}

        {% if result.has_errors %}
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                {% trans "Errors" %}
                            </div>
                        </div>

                        <div class="card-body">
                            <ul>
                                {% for error in result.base_errors %}
                                    <li>
                                        {{ error.error }}
                                        <div class="language-bash traceback">
                                            <div class="highlight">
                                                {{ error.traceback|linebreaks }}
                                            </div>
                                        </div>
                                    </li>
                                {% endfor %}
                                {% for line, errors in result.row_errors %}
                                    {% for error in errors %}
                                        <li>
                                            {% trans "Line number" %}: {{ line }} - <span class="text-danger">{{ error.error }}</span>
                                            <div>
                                                <code>{{ error.row.values|join:", " }}</code>
                                            </div>
                                            <div class="language-bash traceback">
                                                <div class="highlight">
                                                    {{ error.traceback|linebreaks }}
                                                </div>
                                            </div>
                                        </li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

        {% elif result.has_validation_errors %}

            <h2>{% trans "Some rows failed to validate" %}</h2>

            <p>{% trans "Please correct these errors in your data where possible, then reupload it using the form above." %}</p>

            <table class="import-preview">
                <thead>
                <tr>
                    <th>{% trans "Row" %}</th>
                    <th>{% trans "Errors" %}</th>
                    {% for field in result.diff_headers %}
                        <th>{{ field }}</th>
                    {% endfor %}
                </tr>
                </thead>
                <tbody>
                {% for row in result.invalid_rows %}
                    <tr>
                        <td>{{ row.number }} </td>
                        <td class="errors">
                            <span class="validation-error-count">{{ row.error_count }}</span>
                            <div class="validation-error-container">
                                <ul class="validation-error-list">
                                    {% for field_name, error_list in row.field_specific_errors.items %}
                                        <li>
                                            <span class="validation-error-field-label">{{ field_name }}</span>
                                            <ul>
                                                {% for error in error_list %}
                                                    <li>{{ error }}</li>
                                                {% endfor %}
                                            </ul>
                                        </li>
                                    {% endfor %}
                                    {% if row.non_field_specific_errors %}
                                        <li>
                                            <span class="validation-error-field-label">{% trans "Non field specific" %}</span>
                                            <ul>
                                                {% for error in row.non_field_specific_errors %}
                                                    <li>{{ error }}</li>
                                                {% endfor %}
                                            </ul>
                                        </li>
                                    {% endif %}
                                </ul>
                            </div>
                        </td>
                        {% for field in row.values %}
                            <td>{{ field }}</td>
                        {% endfor %}
                    </tr>
                {% endfor %}
                </tbody>
            </table>

        {% else %}

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                {% trans "Preview" %}
                            </div>
                        </div>
                        <div class="card-body table-responsive p-0" style="overflow-x:auto;">
                            <table class="table table-hover text-nowrap import-preview">
                                <thead>
                                    <tr>
                                        <th></th>
                                        {% for field in result.diff_headers %}
                                            <th>{{ field }}</th>
                                        {% endfor %}
                                    </tr>
                                </thead>
                                {% for row in result.valid_rows %}
                                    <tr class="{{ row.import_type }}">
                                        <td class="import-type">
                                            {% if row.import_type == 'new' %}
                                                {% trans "New" %}
                                            {% elif row.import_type == 'skip' %}
                                                {% trans "Skipped" %}
                                            {% elif row.import_type == 'delete' %}
                                                {% trans "Delete" %}
                                            {% elif row.import_type == 'update' %}
                                                {% trans "Update" %}
                                            {% endif %}
                                        </td>
                                        {% for field in row.diff %}
                                            <td>{{ field }}</td>
                                        {% endfor %}
                                    </tr>
                                {% endfor %}
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        {% endif %}
    {% endif %}
    </div>
{% endblock %}

{% block extrajs %}
    {{  block.super }}
    <script type="text/javascript" src="{% static 'vendor/select2/js/select2.min.js' %}"></script>
    <script type="text/javascript" src="{% static 'jazzmin/js/change_form.js' %}"></script>
{% endblock %}

{% extends "admin/filer/change_form.html" %}
{% load admin_modify static %}

{% block extrahead %}
    {{ block.super }}

    {# upload stuff #}
    <script type="text/javascript">
        var __jQuery = django.jQuery;
        window.jQuery = (__jQuery) ? __jQuery : window.jQuery || undefined;
        window.$ = window.jQuery;
    </script>
    <script src="{% static 'filer/js/libs/class.min.js' %}"></script>
    <script src="{% static 'filer/js/libs/jquery-ui.min.js' %}"></script>
    <script src="{% static 'filer/js/libs/jquery.cookie.min.js' %}"></script>
    <script src="{% static 'filer/js/libs/fileuploader.min.js' %}"></script>
    <script src="{% static 'filer/js/libs/mediator.min.js' %}"></script>
    <script src="{% static 'filer/js/libs/retina.min.js' %}"></script>
    <script src="{% static 'admin/js/admin/RelatedObjectLookups.js' %}"></script>
    <script src="{% static 'filer/js/addons/popup_handling.js' %}"></script>
    <script src="{% static 'filer/js/addons/focal-point.js' %}"></script>
    <script src="{% static 'filer/js/addons/toggler.js' %}"></script>
    <script src="{% static 'filer/js/base.js' %}"></script>
    <script type="text/javascript">
        var __jQuery;
        var __$;
        // reassign jQuery if jQuery is already loaded
        __jQuery = (window.jQuery) ? window.jQuery.noConflict(true) : undefined;
        __$ = __jQuery;
    </script>
{% endblock %}

{% block submit_buttons_bottom %}
    {% include "admin/filer/tools/detail_info.html" %}
    {{ block.super }}
{% endblock %}
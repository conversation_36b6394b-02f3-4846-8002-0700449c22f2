{% load static %}
<!DOCTYPE html>
<html lang="zxx" class="no-js">

<head>
	<!-- Mobile Specific Meta -->
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<!-- Favicon-->
	<link rel="shortcut icon" href="img/fav.png">
	<!-- Author Meta -->
	<meta name="author" content="CodePixar">
	<!-- Meta Description -->
	<meta name="description" content="">
	<!-- Meta Keyword -->
	<meta name="keywords" content="">
	<!-- meta character set -->
	<meta charset="UTF-8">
	<!-- Site Title -->
	<title>ecom Shop</title>
	<!--
		CSS
		============================================= -->
	<link rel="stylesheet" href=" {% static 'css/linearicons.css' %}">
	<link rel="stylesheet" href=" {% static 'css/font-awesome.min.css' %}">
	<link rel="stylesheet" href=" {% static 'css/themify-icons.css' %}">
	<link rel="stylesheet" href=" {% static 'css/bootstrap.css' %}">
	<link rel="stylesheet" href=" {% static 'css/owl.carousel.css'%}">
	<link rel="stylesheet" href=" {% static 'css/nice-select.css'%}">
	<link rel="stylesheet" href=" {% static 'css/nouislider.min.css'%}">
	<link rel="stylesheet" href=" {% static 'css/ion.rangeSlider.css'%}" />
	<link rel="stylesheet" href=" {% static 'css/ion.rangeSlider.skinFlat.css'%}" />
	<link rel="stylesheet" href=" {% static 'css/magnific-popup.css'%}">
	<link rel="stylesheet" href=" {% static 'css/main.css'%}">

	<!-- Custom Cart Badge Styling -->
	<style>
		/* Cart Link */
		.cart-link {
			display: inline-block;
			padding: 12px;
			text-decoration: none;
			transition: all 0.3s ease;
		}

		.cart-link:hover {
			text-decoration: none;
			transform: scale(1.05);
		}

		/* Cart Icon - Now the relative container */
		.cart-icon {
			position: relative;
			display: inline-block;
			font-size: 24px;
			color: #333;
			transition: color 0.3s ease;
		}

		.cart-link:hover .cart-icon {
			color: #007bff;
		}

		/* Professional Cart Badge */
		.cart-badge {
			position: absolute;
			top: -2px;
			right: -2px;
			background: linear-gradient(135deg, #ffba00, #ff6c00);
			color: white;
			font-size: 10px;
			font-weight: 700;
			min-width: 16px;
			height: 16px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 2px 6px rgba(255, 186, 0, 0.4);
			border: 1px solid white;
			animation: pulse 2s infinite;
			z-index: 999;
		}

		/* Pulse Animation */
		@keyframes pulse {
			0% {
				box-shadow: 0 3px 8px rgba(255, 186, 0, 0.4);
			}
			50% {
				box-shadow: 0 4px 16px rgba(255, 186, 0, 0.6);
				transform: scale(1.05);
			}
			100% {
				box-shadow: 0 3px 8px rgba(255, 186, 0, 0.4);
			}
		}

		/* Badge for larger numbers */
		.cart-badge.large-count {
			min-width: 24px;
			height: 24px;
			border-radius: 12px;
			font-size: 10px;
		}

		/* Mobile Responsive */
		@media (max-width: 768px) {
			.cart-icon {
				font-size: 18px;
			}

			.cart-badge {
				min-width: 14px;
				height: 14px;
				font-size: 9px;
				top: -1px;
				right: -1px;
			}
		}

		/* Dark theme support */
		@media (prefers-color-scheme: dark) {
			.cart-icon {
				color: #fff;
			}
		}
	</style>

	<!-- Django Messages Styling -->
	<style>
		/* Messages Container */
		.messages-container {
			position: fixed;
			top: 20px;
			right: 20px;
			z-index: 9999;
			max-width: 400px;
			width: 100%;
		}

		/* Alert Styling */
		.alert {
			padding: 15px 20px;
			margin-bottom: 15px;
			border: none;
			border-radius: 8px;
			font-size: 14px;
			font-weight: 500;
			box-shadow: 0 4px 12px rgba(0,0,0,0.15);
			animation: slideInRight 0.5s ease-out;
			position: relative;
		}

		/* Success Message */
		.alert-success {
			background: linear-gradient(135deg, #28a745, #20c997);
			color: white;
			border-left: 4px solid #1e7e34;
		}

		/* Error Message */
		.alert-error, .alert-danger {
			background: linear-gradient(135deg, #dc3545, #c82333);
			color: white;
			border-left: 4px solid #bd2130;
		}

		/* Warning Message */
		.alert-warning {
			background: linear-gradient(135deg, #ffc107, #e0a800);
			color: #212529;
			border-left: 4px solid #d39e00;
		}

		/* Info Message */
		.alert-info {
			background: linear-gradient(135deg, #17a2b8, #138496);
			color: white;
			border-left: 4px solid #117a8b;
		}

		/* Close Button */
		.btn-close {
			position: absolute;
			top: 10px;
			right: 15px;
			background: none;
			border: none;
			font-size: 18px;
			color: inherit;
			opacity: 0.7;
			cursor: pointer;
			padding: 0;
			width: 20px;
			height: 20px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.btn-close:hover {
			opacity: 1;
		}

		/* Animation */
		@keyframes slideInRight {
			from {
				transform: translateX(100%);
				opacity: 0;
			}
			to {
				transform: translateX(0);
				opacity: 1;
			}
		}

		/* Auto-hide animation */
		.alert.fade-out {
			animation: slideOutRight 0.5s ease-out forwards;
		}

		@keyframes slideOutRight {
			from {
				transform: translateX(0);
				opacity: 1;
			}
			to {
				transform: translateX(100%);
				opacity: 0;
			}
		}

		/* Mobile Responsive */
		@media (max-width: 768px) {
			.messages-container {
				top: 10px;
				right: 10px;
				left: 10px;
				max-width: none;
			}

			.alert {
				padding: 12px 15px;
				font-size: 13px;
			}
		}
	</style>
</head>

<body>

	<!-- Start Header Area -->
	<header class="header_area sticky-header">
		<div class="main_menu">
			<nav class="navbar navbar-expand-lg navbar-light main_box">
				<div class="container">
					<!-- Brand and toggle get grouped for better mobile display -->
					<a class="navbar-brand logo_h" href="{% url "home" %}"><img src="{% static 'img/logo.png' %}" alt=""></a>
					<button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
					 aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
						<span class="icon-bar"></span>
						<span class="icon-bar"></span>
						<span class="icon-bar"></span>
					</button>
					<!-- Collect the nav links, forms, and other content for toggling -->
					<div class="collapse navbar-collapse offset" id="navbarSupportedContent">
						<ul class="nav navbar-nav menu_nav ml-auto">
							<li class="nav-item active"><a class="nav-link" href="{% url "home" %}">Home</a></li>
							<li class="nav-item"><a class="nav-link" href="{% url "store" %}">Shop</a></li>
							<li class="nav-item submenu dropdown">
								<a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true"
								 aria-expanded="false">Category</a>
								<ul class="dropdown-menu">
									{% for category in links %}
										<li class="nav-item"><a class="nav-link" href="{{ category.get_url}}">{{ category.category_name}}</a></li>
									{% endfor %}
								</ul>
							</li>
							<li class="nav-item"><a class="nav-link" href="contact.html">Contact</a></li>
							{% if user.is_authenticated %}
							<li class="nav-item"><a class="nav-link" href="#">Dashboard</a></li>
							<li class="nav-item"><a class="nav-link" href="{% url "logout" %}">Logout</a></li>
							{% else %}
							<li class="nav-item"><a class="nav-link" href="{% url "login" %}">Login</a></li>
							<li class="nav-item"><a class="nav-link" href="{% url "register" %}">Register</a></li>
							{% endif %}
						</ul>
						<ul class="nav navbar-nav navbar-right">
							<li class="nav-item">
								<a href="{% url "cart" %}" class="cart-link">
									<span class="ti-bag cart-icon">
						             {% if cart_count > 0 %}
                                       <span class="cart-badge">{{ cart_count }}</span>
                                     {% endif %}
									</span>
								</a>
							</li>
							<li class="nav-item">
								<button class="search"><span class="lnr lnr-magnifier" id="search"></span></button>
							</li>
						</ul>
					</div>
				</div>
			</nav>
		</div>
		<div class="search_input" id="search_input_box">
			<div class="container">
				<form class="d-flex justify-content-between" action="{% url "search" %}" method="GET">
					<input type="text" class="form-control" id="search_input" placeholder="Search Here" name="q">
					<button type="submit" class="btn"></button>
					<span class="lnr lnr-cross" id="close_search" title="Close Search"></span>
				</form>
			</div>
		</div>
	</header>
	<!-- End Header Area -->

    <!-- Django Messages -->
    {% if messages %}
        <div class="messages-container">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    <i class="fa fa-check-circle" style="margin-right: 8px;"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div> 
            {% endfor %}
        </div>
    {% endif %}

    {% block content %}

    {% endblock content %}

    <!-- start footer Area -->
	<footer class="footer-area section_gap">
		<div class="container">
			<div class="row">
				<div class="col-lg-3  col-md-6 col-sm-6">
					<div class="single-footer-widget">
						<h6>About Us</h6>
						<p>
							Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore dolore
							magna aliqua.
						</p>
					</div>
				</div>
				<div class="col-lg-4  col-md-6 col-sm-6">
					<div class="single-footer-widget">
						<h6>Newsletter</h6>
						<p>Stay update with our latest</p>
						<div class="" id="mc_embed_signup">

							<form target="_blank" novalidate="true" action="https://spondonit.us12.list-manage.com/subscribe/post?u=1462626880ade1ac87bd9c93a&amp;id=92a4423d01"
							 method="get" class="form-inline">

								<div class="d-flex flex-row">

									<input class="form-control" name="EMAIL" placeholder="Enter Email" onfocus="this.placeholder = ''" onblur="this.placeholder = 'Enter Email '"
									 required="" type="email">


									<button class="click-btn btn btn-default"><i class="fa fa-long-arrow-right" aria-hidden="true"></i></button>
									<div style="position: absolute; left: -5000px;">
										<input name="b_36c4fd991d266f23781ded980_aefe40901a" tabindex="-1" value="" type="text">
									</div>

									<!-- <div class="col-lg-4 col-md-4">
												<button class="bb-btn btn"><span class="lnr lnr-arrow-right"></span></button>
											</div>  -->
								</div>
								<div class="info"></div>
							</form>
						</div>
					</div>
				</div>
				<div class="col-lg-3  col-md-6 col-sm-6">
					<div class="single-footer-widget mail-chimp">
						<h6 class="mb-20">Instragram Feed</h6>
						<ul class="instafeed d-flex flex-wrap">
							<li><img src="img/i1.jpg" alt=""></li>
							<li><img src="img/i2.jpg" alt=""></li>
							<li><img src="img/i3.jpg" alt=""></li>
							<li><img src="img/i4.jpg" alt=""></li>
							<li><img src="img/i5.jpg" alt=""></li>
							<li><img src="img/i6.jpg" alt=""></li>
							<li><img src="img/i7.jpg" alt=""></li>
							<li><img src="img/i8.jpg" alt=""></li>
						</ul>
					</div>
				</div>
				<div class="col-lg-2 col-md-6 col-sm-6">
					<div class="single-footer-widget">
						<h6>Follow Us</h6>
						<p>Let us be social</p>
						<div class="footer-social d-flex align-items-center">
							<a href="#"><i class="fa fa-facebook"></i></a>
							<a href="#"><i class="fa fa-twitter"></i></a>
							<a href="#"><i class="fa fa-dribbble"></i></a>
							<a href="#"><i class="fa fa-behance"></i></a>
						</div>
					</div>
				</div>
			</div>
			<div class="footer-bottom d-flex justify-content-center align-items-center flex-wrap">
				<p class="footer-text m-0"><!-- Link back to Colorlib can't be removed. Template is licensed under CC BY 3.0. -->
Copyright &copy;<script>document.write(new Date().getFullYear());</script> All rights reserved | This template is made with <i class="fa fa-heart-o" aria-hidden="true"></i> by <a href="https://cormerxe.com" target="_blank">cormerxe</a>
<!-- Link back to Colorlib can't be removed. Template is licensed under CC BY 3.0. -->
</p>
			</div>
		</div>
	</footer>
	<!-- End footer Area -->

	<script src=" {% static 'js/vendor/jquery-2.2.4.min.js'%}"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.11.0/umd/popper.min.js" integrity="sha384-b/U6ypiBEHpOf/4+1nzFpr53nxSS+GLCkfwBdFNTxtclqqenISfwAzpKaMNFNmj4"
	 crossorigin="anonymous"></script>
	<script src=" {% static 'js/vendor/bootstrap.min.js'%}"></script>
	<script src=" {% static 'js/jquery.ajaxchimp.min.js'%}"></script>
	<script src=" {% static 'js/jquery.nice-select.min.js'%}"></script>
	<script src=" {% static 'js/jquery.sticky.js'%}"></script>
	<script src=" {% static 'js/nouislider.min.js'%}"></script>
	<script src=" {% static 'js/countdown.js'%}"></script>
	<script src=" {% static 'js/jquery.magnific-popup.min.js'%}"></script>
	<script src=" {% static 'js/owl.carousel.min.js'%}"></script>
	<!--gmaps Js-->
	<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCjCGmQ0Uq4exrzdcL6rvxywDDOvfAu6eE"></script>
	<script src=" {% static 'js/gmaps.min.js'%}"></script>
	<script src=" {% static 'js/main.js'%}"></script>

	<!-- Cart Badge Enhancement Script -->
	<script>
		document.addEventListener('DOMContentLoaded', function() {
			const cartBadge = document.querySelector('.cart-badge');
			if (cartBadge) {
				const count = parseInt(cartBadge.textContent);
				// Add large-count class for numbers > 99
				if (count > 99) {
					cartBadge.classList.add('large-count');
					cartBadge.textContent = '99+';
				} else if (count > 9) {
					cartBadge.classList.add('large-count');
				}
			}
		});
	</script>

	<!-- Django Messages JavaScript -->
	<script>
		document.addEventListener('DOMContentLoaded', function() {
			// Auto-hide messages after 5 seconds
			const alerts = document.querySelectorAll('.alert');
			alerts.forEach(function(alert) {
				// Auto-hide after 5 seconds
				setTimeout(function() {
					alert.classList.add('fade-out');
					setTimeout(function() {
						alert.remove();
					}, 500);
				}, 5000);

				// Close button functionality
				const closeBtn = alert.querySelector('.btn-close');
				if (closeBtn) {
					closeBtn.addEventListener('click', function() {
						alert.classList.add('fade-out');
						setTimeout(function() {
							alert.remove();
						}, 500);
					});
				}
			});
		});
	</script>
</body>

</html>
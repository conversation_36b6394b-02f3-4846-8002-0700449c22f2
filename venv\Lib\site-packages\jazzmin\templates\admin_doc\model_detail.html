{% extends "admin_doc/base_docs.html" %}
{% load i18n %}

{% block extrahead %}
{{ block.super }}
<style type="text/css">
.module table { width:100%; }
.module table p { padding: 0; margin: 0; }
</style>
{% endblock %}

{% block breadcrumbs %}
<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
    <li class="breadcrumb-item"><a href="{% url 'django-admindocs-docroot' %}">{% trans 'Documentation' %}</a></li>
    <li class="breadcrumb-item"><a href="{% url 'django-admindocs-models-index' %}">{% trans 'Models' %}</a></li>
    <li class="breadcrumb-item active">{{ name }}</li>
</ol>
{% endblock %}

{% block title %}{% blocktrans %}Model: {{ name }}{% endblocktrans %}{% endblock %}
{% block content_title %}{{ name }}{% endblock %}

{% block docs_content %}
    <div class="card card-info">
        <div class="card-header"><h4>{% blocktrans %}Description{% endblocktrans %}</h4></div>
        <div class="card-body">
            {{ summary }}
            {{ description }}
        </div>
    </div>

    <div class="card card-success">
        <div class="card-header"><h4>{% trans 'Fields' %}</h4></div>
        <div class="card-body">
            <table class="table table-hover text-nowrap">
                <thead>
                <tr>
                    <th>{% trans 'Field' %}</th>
                    <th>{% trans 'Type' %}</th>
                    <th>{% trans 'Description' %}</th>
                </tr>
                </thead>

                <tbody>
                {% for field in fields|dictsort:"name" %}
                <tr>
                    <td>{{ field.name }}</td>
                    <td>{{ field.data_type }}</td>
                    <td>{{ field.verbose }}{% if field.help_text %} - {{ field.help_text|safe }}{% endif %}</td>
                </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    {% if methods %}
    <div class="card card-warning">
        <div class="card-header"><h4>{% trans 'Methods with arguments' %}</h4></div>
        <div class="card-body">
            <table class="table table-hover text-nowrap">
                <thead>
                <tr>
                    <th>{% trans 'Method' %}</th>
                    <th>{% trans 'Arguments' %}</th>
                    <th>{% trans 'Description' %}</th>
                </tr>
                </thead>

                <tbody>
                {% for method in methods|dictsort:"name" %}
                <tr>
                    <td>{{ method.name }}</td>
                    <td>{{ method.arguments }}</td>
                    <td>{{ method.verbose }}</td>
                </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    <p class="small"><a href="{% url 'django-admindocs-models-index' %}">&lsaquo; {% trans 'Back to Model documentation' %}</a></p>
{% endblock %}

{% extends "base.html" %}
{% load static %}

{% block content %}
<!-- Custom Registration Styles -->
<style>
.register-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 60px 0;
}

.register-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
    max-width: 1000px;
    margin: 0 auto;
}

.register-left {
    background: linear-gradient(135deg, #ffba00 0%, #ff6c00 100%);
    color: white;
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    position: relative;
}

.register-left::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.3;
}

.register-left h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.register-left p {
    font-size: 1.1rem;
    opacity: 0.9;
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

.register-right {
    padding: 60px 40px;
}

.register-form h3 {
    color: #333;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 10px;
    text-align: center;
}

.register-form .subtitle {
    color: #666;
    text-align: center;
    margin-bottom: 40px;
    font-size: 1rem;
}

.form-group {
    margin-bottom: 25px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
    font-size: 0.95rem;
}

.form-control {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-control:focus {
    outline: none;
    border-color: #ffba00;
    background: white;
    box-shadow: 0 0 0 3px rgba(255, 186, 0, 0.1);
}

.form-control.error {
    border-color: #dc3545;
    background: #fff5f5;
}

.form-control.success {
    border-color: #28a745;
    background: #f8fff8;
}

.input-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 1.1rem;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 1.1rem;
    padding: 5px;
}

.password-toggle:hover {
    color: #ffba00;
}

.form-row {
    display: flex;
    gap: 20px;
}

.form-row .form-group {
    flex: 1;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 30px;
}

.checkbox-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #ffba00;
}

.checkbox-group label {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
    cursor: pointer;
}

.checkbox-group a {
    color: #ffba00;
    text-decoration: none;
}

.checkbox-group a:hover {
    text-decoration: underline;
}

.register-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #ffba00, #ff6c00);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.register-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(255, 186, 0, 0.3);
}

.register-btn:active {
    transform: translateY(0);
}

.login-link {
    text-align: center;
    color: #666;
    font-size: 0.95rem;
}

.login-link a {
    color: #ffba00;
    text-decoration: none;
    font-weight: 600;
}

.login-link a:hover {
    text-decoration: underline;
}

.strength-meter {
    height: 4px;
    background: #e1e5e9;
    border-radius: 2px;
    margin-top: 8px;
    overflow: hidden;
}

.strength-bar {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-weak { background: #dc3545; width: 25%; }
.strength-fair { background: #ffc107; width: 50%; }
.strength-good { background: #17a2b8; width: 75%; }
.strength-strong { background: #28a745; width: 100%; }

.strength-text {
    font-size: 0.8rem;
    margin-top: 5px;
    font-weight: 500;
}

@media (max-width: 768px) {
    .register-container {
        padding: 20px 15px;
    }

    .register-left, .register-right {
        padding: 40px 30px;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .register-left h2 {
        font-size: 2rem;
    }
}
</style>

<!-- Start Banner Area -->
<section class="banner-area organic-breadcrumb">
    <div class="container">
        <div class="breadcrumb-banner d-flex flex-wrap align-items-center justify-content-end">
            <div class="col-first">
                <h1>Create Account</h1>
                <nav class="d-flex align-items-center">
                    <a href="{% url 'home' %}">Home<span class="lnr lnr-arrow-right"></span></a>
                    <a href="#">Register</a>
                </nav>
            </div>
        </div>
    </div>
</section>
<!-- End Banner Area -->

<!-- Custom CSS for Equal Height Form and Image -->
<style>
.login_box_area .row {
    display: flex;
    align-items: stretch;
}

.login_box_img, .login_form_inner {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.login_box_img {
    position: relative;
    min-height: 600px;
}

.login_form_inner {
    min-height: 600px;
    padding: 40px 30px;
    background: #fff;
    border-radius: 0 10px 10px 0;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.login_form_inner h3 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    font-weight: 600;
}

.login_form .form-group {
    margin-bottom: 20px;
}

.login_form .form-control {
    height: 50px;
    border-radius: 8px;
    border: 2px solid #e1e5e9;
    transition: all 0.3s ease;
}

.login_form .form-control:focus {
    border-color: #ffba00;
    box-shadow: 0 0 0 3px rgba(255, 186, 0, 0.1);
}

.primary-btn {
    width: 100%;
    height: 50px;
    background: linear-gradient(135deg, #ffba00, #ff6c00);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 186, 0, 0.3);
    color: white;
}

.login_form_inner a {
    color: #ffba00;
    text-decoration: none;
    display: block;
    text-align: center;
    font-weight: 500;
}

.login_form_inner a:hover {
    text-decoration: underline;
    color: #ff6c00;
}

@media (max-width: 991px) {
    .login_box_img, .login_form_inner {
        min-height: auto;
    }

    .login_form_inner {
        border-radius: 10px;
        margin-top: 30px;
    }
}
</style>

	<!--================Register Box Area =================-->
	<section class="login_box_area section_gap">
		<div class="container">
			<div class="row">
				<div class="col-lg-6">
					<div class="login_box_img">
						<img class="img-fluid" src="{% static 'img/login.jpg' %}" alt="">
						<div class="hover">
							<h4>Join Our Community!</h4>
							<p>Create your account today and enjoy exclusive deals, fast shipping, and personalized shopping experience tailored just for you.</p>
							<a class="primary-btn" href="#">Already have an account? Login</a>
						</div>
					</div>
				</div>
				<div class="col-lg-6">
					<div class="login_form_inner">
						<h3>Create Your Account</h3>
						<form class="row login_form" action="#" method="post" id="registerForm" novalidate="novalidate">
							{% csrf_token %}

							<!-- First Name -->
							<div class="col-md-6 form-group">
								<input type="text" class="form-control" id="first_name" name="first_name" placeholder="First Name" onfocus="this.placeholder = ''" onblur="this.placeholder = 'First Name'" required>
							</div>

							<!-- Last Name -->
							<div class="col-md-6 form-group">
								<input type="text" class="form-control" id="last_name" name="last_name" placeholder="Last Name" onfocus="this.placeholder = ''" onblur="this.placeholder = 'Last Name'" required>
							</div>

							<!-- Email -->
							<div class="col-md-12 form-group">
								<input type="email" class="form-control" id="email" name="email" placeholder="Email Address" onfocus="this.placeholder = ''" onblur="this.placeholder = 'Email Address'" required>
							</div>

							<!-- Phone Number -->
							<div class="col-md-12 form-group">
								<input type="tel" class="form-control" id="phone_number" name="phone_number" placeholder="Phone Number" onfocus="this.placeholder = ''" onblur="this.placeholder = 'Phone Number'">
							</div>

							<!-- Date of Birth -->
							<div class="col-md-12 form-group">
								<input type="date" class="form-control" id="date_of_birth" name="date_of_birth" placeholder="Date of Birth">
							</div>

							<!-- Gender -->
							<div class="col-md-12 form-group">
								<select class="form-control" id="gender" name="gender">
									<option value="">Select Gender</option>
									<option value="male">Male</option>
									<option value="female">Female</option>
									<option value="other">Other</option>
									<option value="prefer_not_to_say">Prefer not to say</option>
								</select>
							</div>

							<!-- Password -->
							<div class="col-md-12 form-group">
								<input type="password" class="form-control" id="password1" name="password1" placeholder="Password" onfocus="this.placeholder = ''" onblur="this.placeholder = 'Password'" required>
							</div>

							<!-- Confirm Password -->
							<div class="col-md-12 form-group">
								<input type="password" class="form-control" id="password2" name="password2" placeholder="Confirm Password" onfocus="this.placeholder = ''" onblur="this.placeholder = 'Confirm Password'" required>
							</div>
							<!-- Terms and Conditions -->
							<div class="col-md-12 form-group">
								<div class="creat_account">
									<input type="checkbox" id="terms" name="terms" required>
									<label for="terms">I agree to the <a href="#" style="color: #ffba00;">Terms of Service</a> and <a href="#" style="color: #ffba00;">Privacy Policy</a></label>
								</div>
							</div>

							<!-- Register Button -->
							<div class="col-md-12 form-group">
								<button type="submit" value="submit" class="primary-btn">Create Account</button>
								<a href="#">Already have an account? Sign In</a>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!--================End Login Box Area =================-->



	{% endblock content%}
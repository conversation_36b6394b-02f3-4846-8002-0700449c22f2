{% extends "admin/base_site.html" %}
{% load i18n static jazzmin %}
{% get_jazzmin_ui_tweaks as jazzmin_ui %}

{# TODO: This should come through somewhere, also admin docs :( #}
{% block userlinks %}
    {% url 'django-admindocs-docroot' as docsroot %}
    {% if docsroot %}<a href="{{ docsroot }}">{% trans 'Documentation' %}</a> / {% endif %}
    {% trans 'Change password' %} / <a href="{% url 'admin:logout' %}">{% trans 'Log out' %}</a>
{% endblock %}

{% block breadcrumbs %}
<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
    <li class="breadcrumb-item active">{% trans 'Password change' %}</li>
</ol>
{% endblock %}

{% block title %}{{ title }}{% endblock %}
{% block content_title %}{{ title }}{% endblock %}

{% block content %}

    <div class="col-12">
        <div class="card">
            <div class="card-header with-border">
                <h4 class="card-title">
                    <p>{% trans "Please enter your old password, for security's sake, and then enter your new password twice so we can verify you typed it in correctly." %}</p>
                </h4>
            </div>

            <div class="card-body">
                <div id="content-main">
                    <form method="post">{% csrf_token %}
                        <div>
                            {% if form.errors %}
                            <div class="callout callout-danger">
                                {% if errors|length == 1 %}{% trans "Please correct the error below." %}{% else %}{% trans "Please correct the errors below." %}{% endif %}
                            </div>
                            {% endif %}

                            <fieldset class="module aligned">
                                <div class="row form-group">
                                    <div class="col-12 col-md-2">
                                        <label class="control-label float-md-right">
                                            {{ form.old_password.field.label }}
                                        </label>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        {{ form.old_password }}
                                        {% if form.old_password.help_text %}
                                        <div class="help-block">{{ form.old_password.help_text|safe }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-12 col-md-4">
                                        <div class="help-block text-red">
                                            {{ form.old_password.errors }}
                                        </div>
                                    </div>
                                </div>

                                <div class="row form-group">
                                    <div class="col-12 col-md-2">
                                        <label class="control-label float-md-right">
                                            {{ form.new_password1.field.label }}
                                        </label>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        {{ form.new_password1 }}
                                        {% if form.new_password1.help_text %}
                                        <div class="help-block">{{ form.new_password1.help_text|safe }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-12 col-md-4">
                                        <div class="help-block text-red">
                                            {{ form.new_password1.errors }}
                                        </div>
                                    </div>
                                </div>

                                <div class="row form-group">
                                    <div class="col-12 col-md-2">
                                        <label class="control-label float-md-right">
                                            {{ form.new_password2.field.label }}
                                        </label>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        {{ form.new_password2 }}
                                        {% if form.new_password1.help_text %}
                                        <div class="help-block">{{ form.new_password2.help_text|safe }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-12 col-md-4">
                                        <div class="help-block text-red">
                                            {{ form.new_password2.errors }}
                                        </div>
                                    </div>
                                </div>

                            </fieldset>
                            <div class="row">
                                <div class="submit-row col-md-12 col-md-push-2">
                                    <input type="submit" value="{% trans 'Change password' %}" class="btn {{ jazzmin_ui.button_classes.primary }}">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

{% endblock %}


{% block extrajs %}
<script>
    $('[required]').before('<span class="text-red" style="margin-left: -10px;">* </span>');
</script>
{% endblock %}

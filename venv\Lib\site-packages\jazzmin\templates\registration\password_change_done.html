{% extends "admin/base_site.html" %}
{% load i18n %}
{% block userlinks %}
    {% url 'django-admindocs-docroot' as docsroot %}
    {% if docsroot %}<a href="{{ docsroot }}">{% trans 'Documentation' %}</a> / {% endif %}
    {% trans 'Change password' %} /
    <a href="{% url 'admin:logout' %}">{% trans 'Log out' %}</a>
{% endblock %}
{% block breadcrumbs %}
<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
    <li class="breadcrumb-item active">{% trans 'Password change' %}</li>
</ol>
{% endblock %}

{% block title %}{{ title }}{% endblock %}
{% block content_title %} {{ title }} {% endblock %}
{% block content %}
    <div id="content-main" class="col-12">
        <div class="callout callout-success">
            <p>{% trans 'Your password was changed.' %}</p>
        </div>
    </div>
{% endblock %}

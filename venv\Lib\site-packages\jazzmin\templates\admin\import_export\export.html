{% extends "admin/import_export/base.html" %}
{% load i18n admin_urls static import_export_tags jazzmin %}
{% get_jazzmin_ui_tweaks as jazzmin_ui %}

{% block extrastyle %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'vendor/select2/css/select2.min.css' %}">
{% endblock %}

{% block breadcrumbs_last %}
    {% trans "Export" %}
{% endblock %}

{% block content %}
    <div class="col-12">
        <form action="" method="POST" class="form-horizontal">
            {% csrf_token %}
            <div class="row">
                <div class="col-12 col-lg-9">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                Export
                            </div>
                        </div>
                        <div class="card-body">
                            {% for field in form %}
                                <div class="form-group field-name">
                                    <div class="row">
                                    {{ field.errors }}
                                        <div class="col-sm-2 text-left">
                                            {{ field.label_tag }}
                                        </div>
                                        <div class="col-sm-10 text-left">
                                            {{ field }}
                                        </div>

                                        <div class="help-block red">
                                            {% if field.field.help_text %}
                                                <img src="{% static "admin/img/icon-unknown.svg" %}" class="help help-tooltip" width="10" height="10"  alt="({{ field.field.help_text|striptags }})" title="{{ field.field.help_text|striptags }}">
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <div class="col-12 col-lg-3">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-edit"></i>
                                Actions
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <input type="submit" class="btn {{ jazzmin_ui.button_classes.success }} form-control" value="{% trans "Submit" %}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}

{% block extrajs %}
    {{  block.super }}
    <script type="text/javascript" src="{% static 'vendor/select2/js/select2.min.js' %}"></script>
    <script type="text/javascript" src="{% static 'jazzmin/js/change_form.js' %}"></script>
{% endblock %}

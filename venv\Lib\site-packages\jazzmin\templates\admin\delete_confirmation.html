{% extends "admin/base_site.html" %}
{% load i18n admin_urls static jazzmin %}
{% get_jazzmin_ui_tweaks as jazzmin_ui %}

{% block extrahead %}
{{ block.super }}
{{ media }}
<script type="text/javascript" src="{% static 'admin/js/cancel.js' %}"></script>
{% endblock %}

{% block bodyclass %}{{ block.super }} app-{{ opts.app_label }} model-{{ opts.model_name }} delete-confirmation{% endblock %}

{% block breadcrumbs %}
<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
    <li class="breadcrumb-item"><a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a></li>
    <li class="breadcrumb-item"><a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a></li>
    <li class="breadcrumb-item"><a href="{% url opts|admin_urlname:'change' object.pk|admin_urlquote %}">{{ object|truncatewords:"18" }}</a></li>
    <li class="breadcrumb-item active">{% trans 'Delete' %}</li>
</ol>
{% endblock %}

{% block content_title %} {% trans 'Delete' %} {% endblock %}

{% block content %}

<div class="col-12">
    <div class="card card-danger card-outline">
        <div class="card-header with-border">
            <h4 class="card-title">
                {% trans 'Delete' %} {{ object|truncatewords:"18" }}
            </h4>
        </div>

        <div class="card-body">
            <div id="content-main">
                {% if perms_lacking %}
                    <p>{% blocktrans with escaped_object=object %}Deleting the {{ object_name }} '{{ escaped_object }}' would result in deleting related objects, but your account doesn't have permission to delete the following types of objects:{% endblocktrans %}</p>
                    <ol>
                        {% for obj in perms_lacking %}
                        <li>{{ obj }}</li>
                        {% endfor %}
                    </ol>
                {% elif protected %}
                    <p>{% blocktrans with escaped_object=object %}Deleting the {{ object_name }} '{{ escaped_object }}' would require deleting the following protected related objects:{% endblocktrans %}</p>
                    <ol>
                        {% for obj in protected %}
                        <li>{{ obj }}</li>
                        {% endfor %}
                    </ol>
                {% else %}
                    <p>{% blocktrans with escaped_object=object %}Are you sure you want to delete the {{ object_name }} "{{ escaped_object }}"? All of the following related items will be deleted:{% endblocktrans %}</p>
                    <div class="row">
                        <div class="col-12 col-sm-9">
                            <h4>{% trans "Objects" %}</h4>
                            <ol>{{ deleted_objects|unordered_list }}</ol>
                        </div>
                        <div class="col-12 col-sm-3">
                            {% include "admin/includes/object_delete_summary.html" %}
                            <form method="post">{% csrf_token %}
                                <input type="hidden" name="post" value="yes">
                                {% if is_popup %}<input type="hidden" name="{{ is_popup_var }}" value="1">{% endif %}
                                {% if to_field %}<input type="hidden" name="{{ to_field_var }}" value="{{ to_field }}">{% endif %}
                                <div class="form-group">
                                    <input type="submit" class="btn {{ jazzmin_ui.button_classes.danger }} form-control" value="{% trans 'Yes, I’m sure' %}">
                                </div>
                                <div class="form-group">
                                    <a href="#" class="btn {{ jazzmin_ui.button_classes.danger }} cancel-link form-control">{% trans "No, take me back" %}</a>
                                </div>
                            </form>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% endblock %}

body {
	line-height: 24px;
	font-size: 14px;
	font-family: $primary-font;
	font-weight: 400;
	color: $text-color;
	background: #fff;
}
html,
body {
	height: 100%;
}
ul {
	margin: 0;
	padding: 0;
	list-style: none;
}
h1,
h2,
h3,
h4,
h5,
h6 {
	font-family: $title-font;
	color: $title-color;
	font-weight: 500;
	line-height: 1.2 !important;
}

.list {
	list-style: none;
	margin: 0px;
	padding: 0px;
}

a {
	text-decoration: none;
	transition: all 0.3s ease-in-out;
	&:hover,
	&:focus {
		text-decoration: none;
		outline: none;
	}
}

button:focus {
	outline: none;
	box-shadow: none;
}

.overflow-hidden {
	overflow: hidden;
}

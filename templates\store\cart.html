{% extends "base.html" %}
{% load static %}

{% block content %}

<!-- Custom CSS for Cart Page -->
<style>
/* Quantity controls */
.quantity-controls {
    display: flex;
    align-items: center;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    max-width: 140px;
}

.quantity-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    color: #495057;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.quantity-btn:hover {
    background: #007bff;
    color: white;
    text-decoration: none;
}

.quantity-btn:active {
    transform: scale(0.95);
}

.quantity-input {
    width: 60px;
    height: 40px;
    border: none;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    background: #fff;
    outline: none;
    -moz-appearance: textfield; /* Firefox */
}

.quantity-input::-webkit-outer-spin-button,
.quantity-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.decrease-btn {
    border-right: 1px solid #e9ecef;
}

.increase-btn {
    border-left: 1px solid #e9ecef;
}

.decrease-btn:hover {
    background: #dc3545;
}

.increase-btn:hover {
    background: #28a745;
}

/* Remove item button */
.remove-item-btn {
    background: #dc3545;
    color: white !important;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    margin-left: 15px;
}

.remove-item-btn:hover {
    background: #c82333;
    color: white !important;
    text-decoration: none;
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.remove-item-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.25);
}

.remove-item-btn i {
    font-size: 14px;
}

/* Responsive design */
@media (max-width: 768px) {
    .quantity-controls {
        max-width: 120px;
    }

    .quantity-btn {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }

    .quantity-input {
        width: 50px;
        height: 35px;
        font-size: 14px;
    }

    .remove-item-btn i {
        font-size: 14px;
    }
}
</style>
        <!-- Start Banner Area -->
    <section class="banner-area organic-breadcrumb">
        <div class="container">
            <div class="breadcrumb-banner d-flex flex-wrap align-items-center justify-content-end">
                <div class="col-first">
                    <h1>Shopping Cart</h1>
                    <nav class="d-flex align-items-center">
                        <a href="{% url "home" %}">Home<span class="lnr lnr-arrow-right"></span></a>
                        <a href="{% url "cart" %}">Cart</a>
                    </nav>
                </div>
            </div>
        </div>
    </section>
    <!-- End Banner Area -->

    <!--================Cart Area =================-->
    <section class="cart_area">
        <div class="container">
            <div class="cart_inner">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th scope="col">Product</th>
                                <th scope="col">Price</th>
                                <th scope="col">Quantity</th>
                                <th scope="col">Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for cart_item in cart_items %}
                                <tr>
                                    <td>
                                        <div class="media">
                                            <div class="d-flex">
                                                <img src="{{ cart_item.product.images.url }}" style="width: 70px; height: 70px;" alt="">
                                            </div>
                                            <div class="media-body">
                                                <a href="{{ cart_item.product.get_url }}" class="text-dark text-decoration-none">{{ cart_item.product.product_name }}</a>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <h5>${{ cart_item.product.price }}</h5>
                                    </td>
                                    <td>
                                        <div class="product_count">
                                            <div class="quantity-controls">
                                                <a href="#" class="quantity-btn decrease-btn ajax-decrease"
                                                   data-product-id="{{ cart_item.product.id }}"
                                                   data-product-name="{{ cart_item.product.product_name }}">
                                                    <i class="lnr lnr-chevron-down"></i>
                                                </a>
                                                <input type="number" name="qty" id="qty-{{ cart_item.product.id }}"
                                                       min="1" max="99" value="{{ cart_item.quantity }}"
                                                       class="quantity-input" readonly>
                                                <a href="#" class="quantity-btn increase-btn ajax-increase"
                                                   data-product-id="{{ cart_item.product.id }}"
                                                   data-product-name="{{ cart_item.product.product_name }}">
                                                    <i class="lnr lnr-chevron-up"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center justify-content-between">
                                            <h5 class="item-subtotal" data-product-id="{{ cart_item.product.id }}">${{ cart_item.sub_total }}</h5>
                                            <a href="#" class="remove-item-btn btn btn-sm ajax-remove"
                                               data-product-id="{{ cart_item.product.id }}"
                                               data-product-name="{{ cart_item.product.product_name }}"
                                               title="Remove item from cart">
                                                <i class="fa fa-trash ms-2"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                            <tr class="bottom_button">
                                <td>
                                    <a class="gray_btn" href="#">Update Cart</a>
                                </td>
                                <td>

                                </td>
                                <td>

                                </td>
                                <td>
                                    <div class="cupon_text d-flex align-items-center">
                                        <input type="text" placeholder="Coupon Code">
                                        <a class="primary-btn" href="#">Apply</a>
                                        <a class="gray_btn" href="#">Close Coupon</a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>

                                </td>
                                <td>

                                </td>

                                <td>
                                    <div class="checkout_btn_inner d-flex align-items-center">
                                        <a class="gray_btn" href="{% url "store" %}">Continue Shopping</a>
                                        <a class="primary-btn" href="{% url 'checkout' %}" onclick="return validateCheckout()">Proceed to checkout</a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>
    <!--================End Cart Area =================-->

<!-- JavaScript for Quantity Controls -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle decrease button clicks
    document.querySelectorAll('.decrease-btn').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const productId = this.getAttribute('data-product-id');
            const quantityInput = document.getElementById('qty-' + productId);
            let currentQty = parseInt(quantityInput.value);

            if (currentQty > 1) {
                quantityInput.value = currentQty - 1;
                updateCartItem(productId, currentQty - 1);
            }
        });
    });

    // Handle increase button clicks (you can also add AJAX here)
    document.querySelectorAll('.increase-btn').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            // Let the default behavior happen (redirect to add_cart URL)
            // Or you can prevent default and use AJAX
        });
    });

    // Handle direct input changes
    document.querySelectorAll('.quantity-input').forEach(function(input) {
        input.addEventListener('change', function() {
            const productId = this.getAttribute('id').replace('qty-', '');
            const newQty = parseInt(this.value);

            if (newQty >= 1 && newQty <= 99) {
                updateCartItem(productId, newQty);
            } else {
                this.value = 1; // Reset to minimum
            }
        });
    });
});

// Function to update cart item (you can implement AJAX here)
function updateCartItem(productId, quantity) {
    // For now, we'll just update the display
    // You can implement AJAX calls to update the backend
    console.log('Updating product ' + productId + ' to quantity ' + quantity);

    // Example: Update subtotal display
    updateSubtotal(productId, quantity);
}

// Function to update subtotal for a specific item
function updateSubtotal(productId, quantity) {
    // This is a basic example - you'll need to get the price from somewhere
    // In a real implementation, you'd get this from the server or store it in data attributes
    const row = document.querySelector(`#qty-${productId}`).closest('tr');
    const priceElement = row.querySelector('td:nth-child(2) h5');
    const totalElement = row.querySelector('td:nth-child(4) h5');

    if (priceElement && totalElement) {
        const price = parseFloat(priceElement.textContent.replace('$', ''));
        const newTotal = (price * quantity).toFixed(2);
        totalElement.textContent = '$' + newTotal;

        // Update grand total
        updateGrandTotal();
    }
}

// Function to update the grand total
function updateGrandTotal() {
    let grandTotal = 0;
    document.querySelectorAll('tbody tr:not(.bottom_button):not(.shipping_area):not(.out_button_area)').forEach(function(row) {
        const totalElement = row.querySelector('td:nth-child(4) h5');
        if (totalElement && totalElement.textContent.includes('$')) {
            const itemTotal = parseFloat(totalElement.textContent.replace('$', ''));
            grandTotal += itemTotal;
        }
    });

    // Update the subtotal display
    const subtotalElement = document.querySelector('tr:not(.shipping_area):not(.out_button_area) td:nth-child(4) h5');
    if (subtotalElement) {
        subtotalElement.textContent = '$' + grandTotal.toFixed(2);
    }
}

// AJAX Cart Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Get CSRF token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    const csrftoken = getCookie('csrftoken');

    // Add toast notification function
    function showToast(message, type = 'success') {
        // Create toast if it doesn't exist
        let toast = document.getElementById('cart-toast');
        if (!toast) {
            toast = document.createElement('div');
            toast.id = 'cart-toast';
            toast.className = 'cart-toast';
            toast.innerHTML = `
                <div class="toast-content">
                    <span class="toast-icon">✓</span>
                    <span class="toast-message"></span>
                </div>
            `;
            document.body.appendChild(toast);
        }

        const toastMessage = toast.querySelector('.toast-message');
        const toastIcon = toast.querySelector('.toast-icon');

        toastMessage.textContent = message;

        // Change color and icon based on type
        if (type === 'error') {
            toast.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
            toastIcon.textContent = '✗';
        } else {
            toast.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
            toastIcon.textContent = '✓';
        }

        // Show toast
        toast.classList.add('show');

        // Hide toast after 3 seconds
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }

    // Update cart display
    function updateCartDisplay(data, productId) {
        // Update cart badge using global function
        if (typeof updateCartBadge === 'function') {
            updateCartBadge(data.cart_count);
        }

        // Update total
        const totalElement = document.querySelector('tr:last-child td:last-child h5');
        if (totalElement) {
            totalElement.textContent = '$' + data.total.toFixed(2);
        }

        // Update item quantity and subtotal
        const quantityInput = document.getElementById(`qty-${productId}`);
        const subtotalElement = document.querySelector(`.item-subtotal[data-product-id="${productId}"]`);

        if (data.item_removed) {
            // Remove the entire row
            const row = quantityInput.closest('tr');
            if (row) {
                row.style.transition = 'opacity 0.3s ease';
                row.style.opacity = '0';
                setTimeout(() => {
                    row.remove();
                    // Check if cart is empty
                    const remainingRows = document.querySelectorAll('tbody tr:not(.bottom_button):not(.shipping_area):not(.out_button_area)');
                    if (remainingRows.length === 0) {
                        location.reload(); // Reload to show empty cart message
                    }
                }, 300);
            }
        } else {
            // Update quantity and subtotal
            if (quantityInput) {
                quantityInput.value = data.item_quantity;
            }
            if (subtotalElement) {
                subtotalElement.textContent = '$' + data.item_subtotal.toFixed(2);
            }
        }
    }

    // Increase quantity AJAX
    document.querySelectorAll('.ajax-increase').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const productId = this.getAttribute('data-product-id');
            const productName = this.getAttribute('data-product-name');

            // Add loading state
            this.style.opacity = '0.7';
            this.style.pointerEvents = 'none';

            fetch(`/cart/add_cart_ajax/${productId}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrftoken,
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                console.log('Increase response:', data); // Debug log
                this.style.opacity = '1';
                this.style.pointerEvents = 'auto';

                if (data.success) {
                    // Update quantity input
                    const quantityInput = document.getElementById(`qty-${productId}`);
                    if (quantityInput) {
                        const newQuantity = parseInt(quantityInput.value) + 1;
                        quantityInput.value = newQuantity;

                        // Update subtotal using the price from the price column
                        const row = quantityInput.closest('tr');
                        const priceElement = row.querySelector('td:nth-child(2) h5');
                        const subtotalElement = row.querySelector('.item-subtotal');

                        if (priceElement && subtotalElement) {
                            const price = parseFloat(priceElement.textContent.replace('$', ''));
                            const newSubtotal = price * newQuantity;
                            subtotalElement.textContent = '$' + newSubtotal.toFixed(2);
                        }
                    }

                    // Update cart badge using global function
                    if (typeof updateCartBadge === 'function') {
                        updateCartBadge(data.cart_count);
                    }

                    // Update total
                    if (data.total !== undefined) {
                        const totalElement = document.querySelector('tr:last-child td:last-child h5');
                        if (totalElement) {
                            totalElement.textContent = '$' + data.total.toFixed(2);
                        }
                    }

                    showToast(`Increased ${productName} quantity`, 'success');
                } else {
                    showToast(data.message || 'Error updating cart', 'error');
                }
            })
            .catch(error => {
                this.style.opacity = '1';
                this.style.pointerEvents = 'auto';
                showToast('Error updating cart', 'error');
            });
        });
    });

    // Decrease quantity AJAX
    document.querySelectorAll('.ajax-decrease').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const productId = this.getAttribute('data-product-id');
            const productName = this.getAttribute('data-product-name');

            // Add loading state
            this.style.opacity = '0.7';
            this.style.pointerEvents = 'none';

            fetch(`/cart/remove_cart_ajax/${productId}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrftoken,
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                this.style.opacity = '1';
                this.style.pointerEvents = 'auto';

                if (data.success) {
                    updateCartDisplay(data, productId);
                    showToast(data.message, 'success');
                } else {
                    showToast(data.message || 'Error updating cart', 'error');
                }
            })
            .catch(error => {
                this.style.opacity = '1';
                this.style.pointerEvents = 'auto';
                showToast('Error updating cart', 'error');
            });
        });
    });

    // Remove item AJAX
    document.querySelectorAll('.ajax-remove').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const productId = this.getAttribute('data-product-id');
            const productName = this.getAttribute('data-product-name');

            // Add loading state
            this.style.opacity = '0.7';
            this.style.pointerEvents = 'none';

            fetch(`/cart/remove_cart_item_ajax/${productId}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrftoken,
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateCartDisplay(data, productId);
                    showToast(data.message, 'success');
                } else {
                    this.style.opacity = '1';
                    this.style.pointerEvents = 'auto';
                    showToast(data.message || 'Error removing item', 'error');
                }
            })
            .catch(error => {
                this.style.opacity = '1';
                this.style.pointerEvents = 'auto';
                showToast('Error removing item', 'error');
            });
        });
    });
});
</script>

<!-- Toast Notification Styles -->
<style>
.cart-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 9999;
    transform: translateX(400px);
    transition: transform 0.3s ease-in-out;
    min-width: 300px;
}

.cart-toast.show {
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.toast-icon {
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.toast-message {
    flex: 1;
    font-weight: 500;
}
</style>

<!-- Checkout Validation Script -->
<script>
// Validate checkout function
function validateCheckout() {
    // Method 1: Check Django template context (most reliable)
    const cartItemCount = {{ cart_items|length|default:0 }};
    const cartTotal = {{ total|default:0 }};

    console.log('Cart validation - Items:', cartItemCount, 'Total:', cartTotal);

    if (cartItemCount === 0) {
        alert('Your cart is empty. Please add items before proceeding to checkout.');
        return false;
    }

    if (cartTotal <= 0) {
        alert('Your cart total must be greater than $0 to proceed to checkout.');
        return false;
    }

    // Method 2: DOM check as backup
    const quantityInputs = document.querySelectorAll('.quantity-input');
    if (quantityInputs.length === 0) {
        alert('No items found in your cart. Please add items before proceeding to checkout.');
        return false;
    }

    return true; // Allow navigation to checkout
}
</script>

{% endblock content %}
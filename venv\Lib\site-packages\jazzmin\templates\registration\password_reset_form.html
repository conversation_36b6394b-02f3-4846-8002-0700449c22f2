{% extends "registration/base.html" %}

{% load i18n jazzmin %}
{% get_jazzmin_ui_tweaks as jazzmin_ui %}

{% block content %}
    <p class="login-box-msg">
        {% blocktrans trimmed %}
            Forgotten your password?
            Enter your email address below, and we’ll
            email instructions for setting a new one.
        {% endblocktrans %}
    </p>
    <form method="post">
        {% csrf_token %}
        {% if form.errors %}
            {% if form.email.errors %}
                <div class="callout callout-danger">
                    <p>{{ form.email.label }}: {{ form.email.errors|join:', ' }}</p>
                </div>
            {% endif %}
            {% if form.non_field_errors %}
                <div class="callout callout-danger">
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        {% endif %}
        <div class="input-group mb-3">
            <input type="text" name="email" class="form-control" placeholder="{{ form.email.label }}" required>
            <div class="input-group-append">
                <div class="input-group-text">
                    <span class="fas fa-envelope"></span>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <button type="submit" class="btn {{ jazzmin_ui.button_classes.primary }} btn-block">
                    {% trans 'Reset my password' %}
                </button>
            </div>
        </div>
    </form>
{% endblock %}

# Generated by Django 5.2.1 on 2025-06-04 11:00

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.Char<PERSON><PERSON>(max_length=128, verbose_name='password')),
                ('first_name', models.<PERSON>r<PERSON><PERSON>(max_length=50)),
                ('lats_name', models.<PERSON>r<PERSON><PERSON>(max_length=50)),
                ('username', models.<PERSON>r<PERSON><PERSON>(max_length=50, unique=True)),
                ('email', models.EmailField(max_length=100, unique=True)),
                ('phone_number', models.Char<PERSON>ield(max_length=50)),
                ('date_joined', models.DateTimeField(auto_now_add=True)),
                ('last_login', models.DateTimeField(auto_now_add=True)),
                ('is_admin', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ('is_staff', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ('is_active', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ('is_superadmin', models.BooleanField(default=False)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]

{% extends "admin/change_form.html" %}
{% load i18n admin_modify static filer_admin_tags %}

{% block breadcrumbs %}
    {% with original as instance %}
        {% include "admin/filer/breadcrumbs.html" %}
    {% endwith %}
{% endblock %}

{% block extrastyle %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{% static 'filer/css/admin_filer.css' %}">
{% endblock %}


{% block after_field_sets %}
    {% filer_admin_context_hidden_formfields %}
{% endblock %}

{% block file_sidebar %}
    {% with original.duplicates as duplicates %}
        {% if duplicates %}
            <div class="file-duplicates">
                <h3>{% trans "Duplicates" %}</h3>
                <ul>
                    {% for duplicate in duplicates %}
                        <li><a href="{{ duplicate.get_admin_change_url }}">{{ duplicate }}</a></li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
    {% endwith %}
{% endblock %}
